import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class CouponsPage extends StatefulWidget {
  const CouponsPage({super.key});

  @override
  State<CouponsPage> createState() => _CouponsPageState();
}

class _CouponsPageState extends State<CouponsPage> {
  late VideoPlayerController _controller;
  bool isPlaying = true;

  @override
  void initState() {
    super.initState();
    _controller =
        VideoPlayerController.asset('assets/categorias/beleza/beleza.mp4')
          ..initialize()
              .then((_) {
                debugPrint('Vídeo inicializado com sucesso!');
                setState(() {
                  _controller.setLooping(true);
                  _controller.play();
                });
              })
              .catchError((error) {
                debugPrint('Erro ao carregar o vídeo: $error');
              });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Coupons Page'),
      ),
      body: Center(
        child: _controller.value.isInitialized
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AspectRatio(
                    aspectRatio: _controller.value.aspectRatio,
                    child: VideoPlayer(_controller),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        onPressed: () {
                          setState(() {
                            if (_controller.value.isPlaying) {
                              _controller.pause();
                              isPlaying = false;
                            } else {
                              _controller.play();
                              isPlaying = true;
                            }
                          });
                        },
                        icon: Icon(
                          isPlaying ? Icons.pause : Icons.play_arrow,
                          size: 32,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          _controller.seekTo(Duration.zero);
                        },
                        icon: const Icon(
                          Icons.replay,
                          size: 32,
                        ),
                      ),
                    ],
                  ),
                ],
              )
            : const Center(
                child: CircularProgressIndicator(),
              ),
      ),
    );
  }
}
