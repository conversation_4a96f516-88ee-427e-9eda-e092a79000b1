import 'dart:io';

import 'package:flutter/material.dart';

import '../../../models/categorias_menu.dart';

class CategoriesController with ChangeNotifier {
  // 🟢 ScrollController e deslocamento do Scroll
  late ScrollController _scrollController;
  double _scrollOffset = 0.0;

  // 🟢 Tamanhos da SliverAppBar e da tela
  double expandedHeight = 365;
  double collapsedHeight = Platform.isAndroid ? 80 : 72;
  double topPadding = 0.0;
  double totalAppBarHeight = 0.0;
  double screenHeight = 0.0;
  double sizeAppBar = 0.0;
  // Padding extra pra Android
  double paddingExtra = Platform.isAndroid ? 8 : 0;

  /// 🔹 Construtor
  CategoriesController() {
    _initScroll();
  }

  /// 🔹 Inicializa o ScrollController e adiciona o listener
  void _initScroll() {
    _scrollController = ScrollController()..addListener(_onScroll);
  }

  /// 🔹 Listener que atualiza o deslocamento do scroll
  void _onScroll() {
    _scrollOffset = _scrollController.offset;
    notifyListeners();
  }

  void scrollToTop() {
    _scrollController.jumpTo(1.0);
    notifyListeners();
  }

  /// 🔹 Atualiza os valores da tela, AppBar e SnackBar dinamicamente
  void updateScreenMetrics(BuildContext context) {
    final appBar = AppBar();
    topPadding = MediaQuery.of(context).padding.top + paddingExtra;
    sizeAppBar = appBar.preferredSize.height;
    screenHeight = MediaQuery.of(context).size.height;
    totalAppBarHeight = topPadding + sizeAppBar;
    expandedHeight = 312 + sizeAppBar + paddingExtra;
    notifyListeners();
  }

  // 🔹 Getter para acessar o ScrollController
  ScrollController get scrollController => _scrollController;

  // 🔹 Getter do offset do scroll
  double get scrollOffset => _scrollOffset;

  /// 🔹 Reseta o ScrollController e mantém o deslocamento
  void resetScroll() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _initScroll();
    notifyListeners();
  }

  /// 🔹 Remove o listener e libera memória quando o Controller for descartado
  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // 🟢  Animações e Transições

  /// 🔹 Cálculo da porcentagem de rolagem
  double get scrollProgress => (_scrollOffset / (expandedHeight - collapsedHeight)).clamp(0.0, 1.0);

  /// 🔹 Opacidade dos itens da SliverAppBar
  double get opacity => scrollProgress;

  /// 🔹 Opacidade dos itens do CategoryInfoCard
  double get opacityCard => (1.0 - (scrollProgress / 0.98)).clamp(0.0, 1.0);

  /// 🔹 Deslocamento lateral dos itens da SliverAppBar
  double get titleOffset => 8 * scrollProgress;
  double get followButtonOffset => -8 * scrollProgress;

  /// 🔹 Deslocamento do fundo da tela ao rolar
  double get moveBackgroundDown {
    double maxTranslateY = 100.0;
    return (_scrollOffset * 0.5).clamp(0.0, maxTranslateY);
  }

  /// 🔹 Deslocamento do Container CategoryInfoCard
  double get containerTranslateY {
    return (-_scrollOffset * 0.2).clamp(-24, 0.0);
  }

  /// 🔹 Deslocamento do Column CategoryInfoCard
  double get columnTranslateY {
    return (-_scrollOffset * 0.2).clamp(-40, 0.0);
  }

  // 🟢  Cores e Estilos

  /// 🔹 Filtro de cor para a tela de detalhes da categoria
  Color getFilteredColor(CategoriaMenu categoria) {
    return Color.alphaBlend(Color.fromRGBO(255, 255, 255, 0.5), categoria.cor);
  }

  //para saber se começa de cima ou nao
}
