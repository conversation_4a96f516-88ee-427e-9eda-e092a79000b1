import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../models/categorias_menu.dart';
import 'ui/pages/categories_page.dart';
import 'ui/pages/details_category_page.dart';

class CategoriesModule extends Module {
  static const String detailsCategoryPage = '/details_category_page';

  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => CategoriesPage(),
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      detailsCategoryPage,
      child: (context) {
        final CategoriaMenu? category =
            r.args.data as CategoriaMenu?; // Cast explícito
        if (category == null) {
          // Tratar o caso onde a categoria é nula, talvez navegar para uma página de erro ou para a lista de categorias
          return const CategoriesPage(); // Exemplo: retorna para a página de listagem de categorias
        }
        return DetailsCategoryPage(
          key: <PERSON><PERSON><PERSON>(category.id),
          category: category,
        );
      },
      transition: TransitionType.fadeIn,
      duration: const Duration(milliseconds: 200),
    );
  }
}
