import '../../../../offers/controllers/products_details_controller.dart';
import '../../../controllers/categories_controller.dart';
import '../../../../../../theme/color_outlet.dart';
import 'package:flutter/material.dart';

class CategoryBackgroundAnimated extends StatelessWidget {
  final CategoriesController? controller;
  final ProductDetailsController? productDetailsController;
  final bool? isDetailsProduct;

  const CategoryBackgroundAnimated({
    super.key,
    this.controller,
    this.productDetailsController,
    this.isDetailsProduct = false,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: Duration(milliseconds: 100),
      transform: isDetailsProduct!
          ? Matrix4.translationValues(
              0,
              productDetailsController!.backgroundTranslateY,
              0,
            )
          : Matrix4.translationValues(0, controller!.moveBackgroundDown, 0),
      child: Container(color: ColorOutlet.surface),
    );
  }
}
