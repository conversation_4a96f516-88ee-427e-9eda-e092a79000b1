import 'package:promobell/src/modules/categories/ui/widgets/detail/category_info_card.dart';

import '../../../../../models/categorias_menu.dart';
import '../../../controllers/categories_controller.dart';
import 'category_image_container.dart';
import '../../../../offers/controllers/offers_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AnimatedCategoryAppBar extends StatefulWidget {
  final CategoriaMenu category;
  final CategoriesController controller;
  final int length;
  final double? topHeight;

  const AnimatedCategoryAppBar({
    super.key,
    required this.category,
    required this.controller,
    required this.length,
    this.topHeight,
  });

  @override
  State<AnimatedCategoryAppBar> createState() => _AnimatedCategoryAppBarState();
}

class _AnimatedCategoryAppBarState extends State<AnimatedCategoryAppBar> {
  OffersController offersController = Modular.get<OffersController>();

  @override
  void initState() {
    super.initState();
    offersController.fetchFollowersCount(widget.category.id);
    offersController.fetchFollowStatus(
      widget.category.id,
      Supabase.instance.client.auth.currentUser?.email ?? '',
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: offersController,
      builder: (context, child) {
        return SliverAppBar(
          pinned: true,
          titleSpacing: 0,
          elevation: 0,
          scrolledUnderElevation: 0,
          automaticallyImplyLeading: false,
          expandedHeight: widget.controller.expandedHeight,
          collapsedHeight: widget.controller.collapsedHeight,
          backgroundColor: widget.controller.scrollProgress > 0.99
              ? widget.controller.getFilteredColor(
                  widget.category,
                )
              : Colors.transparent,
          flexibleSpace: Stack(
            children: [
              CategoryInfoCard(
                widget: widget,
                offersController: offersController,
              ),
              FlexibleSpaceBar(
                expandedTitleScale: 1,
                centerTitle: true,
                titlePadding: EdgeInsets.only(bottom: 248),
                title: CategoryImageContainer(
                  color: widget.controller.getFilteredColor(
                    widget.category,
                  ),
                  category: widget.category,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
