import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/infinite_scroll_loader.dart';

import '../../../../../theme/color_outlet.dart';
import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../core/base/widgets/bottom_navigator_bar_widget.dart';
import '../../../../models/categorias_menu.dart';
import '../../../offers/controllers/offers_controller.dart';
import '../../../offers/ui/widget/offers_page/product_card_widget.dart';
import '../../../offers/ui/widget/shimmer/product_card_shimmer.dart';
import '../../controllers/categories_controller.dart';
import '../widgets/detail/animated_category_app_bar.dart';
import '../widgets/detail/category_background_animated.dart';
import '../widgets/detail/category_header_with_actions.dart';
import '../widgets/detail/persistent_filter_header.dart';

class DetailsCategoryPage extends StatefulWidget {
  final CategoriaMenu category;
  const DetailsCategoryPage({super.key, required this.category});

  @override
  State<DetailsCategoryPage> createState() => _DetailsCategoryPageState();
}

class _DetailsCategoryPageState extends State<DetailsCategoryPage> {
  final CategoriesController controller = Modular.get<CategoriesController>();
  final OffersController controllerOffer = Modular.get<OffersController>();
  final baseController = Modular.get<BaseController>();
  int _totalOfertas = 0;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controllerOffer.setCategoriaMenu(widget.category);
    });

    controllerOffer.produtosPorCategoria.clear();
    controllerOffer.resetPaginationCategoria();
    controller.resetScroll();
    controllerOffer.filterProdutosPorCategoria(widget.category.nome);
    controllerOffer.getTotalPorCategoria(widget.category.nome).then((
      value,
    ) {
      if (mounted) {
        setState(() {
          _totalOfertas = value;
        });
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.updateScreenMetrics(context);
      controllerOffer.getProdutosPorCategoria(widget.category.nome);
      controller.scrollController.addListener(_onScroll);
    });
  }

  void _onScroll() {
    final isFilterActive = controllerOffer.isFiltroPersonalizadoAtivo;
    if (controller.scrollController.position.pixels >= controller.scrollController.position.maxScrollExtent - 300) {
      if (isFilterActive) {
        controllerOffer.buscarProdutosComFiltrosPersonalizados(
          isCategoryFilters: true,
          intCategoria: widget.category.id,
        );
      } else {
        controllerOffer.getProdutosPorCategoria(
          widget.category.nome,
          isLoadMore: true,
        );
      }
    }
  }

  @override
  void dispose() {
    controller.scrollController.removeListener(_onScroll);
    super.dispose();
  }

  // Removido o método _onWillPop pois agora usamos PopScope

  @override
  Widget build(BuildContext context) {
    var paddingBottom = MediaQuery.of(context).padding.bottom;

    return AnimatedBuilder(
      animation: Listenable.merge([controller, controllerOffer]),
      builder: (context, _) {
        final usandoFiltro = controllerOffer.isFiltroPersonalizadoAtivo;
        final produtos = usandoFiltro ? controllerOffer.produtosFiltradosByUser : controllerOffer.produtosPorCategoria;

        return PopScope(
          canPop: true,
          onPopInvokedWithResult: (didPop, result) async {
            if (didPop) return;

            // Verifica se a tela foi aberta por um deeplink
            final isFromDeepLink = Modular.to.path != '/home';

            if (isFromDeepLink) {
              // Se veio de um deeplink, usa o método especial para voltar para a tela de categorias
              baseController.navBackFromCategoryDeepLink();
            } else {
              // Comportamento normal
              // Limpa os filtros
              controllerOffer.limparTodosOsFiltros(
                isCategoryFilters: false,
              );
              controllerOffer.setFilterCategory("Recentes");
              Modular.to.pop();
            }
          },
          child: Scaffold(
            backgroundColor: controller.getFilteredColor(
              widget.category,
            ),
            body: Stack(
              children: [
                Padding(
                  padding: EdgeInsets.only(
                    top: controller.totalAppBarHeight + 150,
                  ),
                  child: CategoryBackgroundAnimated(
                    controller: controller,
                  ),
                ),
                Container(
                  color: controller.scrollProgress > 0.99 ? ColorOutlet.surface : Colors.transparent,
                  height: controller.screenHeight,
                ),
                CustomScrollView(
                  controller: controller.scrollController,
                  physics: const BouncingScrollPhysics(),
                  slivers: [
                    AnimatedCategoryAppBar(
                      category: widget.category,
                      controller: controller,
                      length: _totalOfertas,
                    ),
                    PersistentFilterHeader(
                      controller: controller,
                      widget: widget,
                    ),
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        childCount: produtos.isEmpty ? 1 : produtos.length,
                        (context, index) {
                          if (produtos.isEmpty) {
                            return Padding(
                              padding: const EdgeInsets.all(32),
                              child: Center(
                                child: Text(
                                  'Nenhum produto encontrado',
                                  style: TextStyle(
                                    color: ColorOutlet.contentGhost,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            );
                          }

                          final produto = produtos[index];
                          final isLast = produto == produtos.last;

                          return Container(
                            color: ColorOutlet.surface,
                            padding: isLast
                                ? EdgeInsets.only(
                                    top: 0,
                                    left: 16,
                                    right: 16,
                                    bottom: controllerOffer.isLoadingMoreCategoria && controllerOffer.hasMorCategorias ? 0 : 120 + paddingBottom,
                                  )
                                : const EdgeInsets.only(
                                    bottom: 16,
                                    left: 16,
                                    right: 16,
                                  ),
                            child: controllerOffer.loadingFilterCategory
                                ? const ProductCardShimmer()
                                : ProductCardWidget(
                                    product: produto,
                                  ),
                          );
                        },
                      ),
                    ),
                    InfiniteScrollLoader(
                      loading: controllerOffer.isLoadingMoreCategoria && controllerOffer.hasMorCategorias,
                      paddingBottom: paddingBottom,
                    ),
                  ],
                ),
                CategoryHeaderWithActions(
                  heightAppBar: controller.totalAppBarHeight,
                  controller: controller,
                  category: widget.category,
                  offersController: controllerOffer,
                ),
                CustomNagivationBarWidget(
                  paddingBottom: paddingBottom,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class CustomNagivationBarWidget extends StatelessWidget {
  final double paddingBottom;

  const CustomNagivationBarWidget({
    required this.paddingBottom,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Stack(
        children: [
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 66,
              decoration: BoxDecoration(
                color: ColorOutlet.surface,
                boxShadow: [
                  BoxShadow(
                    color: ColorOutlet.surface,
                    spreadRadius: 24,
                    blurRadius: 24,
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: paddingBottom),
            child: BottomNavigationBarWidget(),
          ),
        ],
      ),
    );
  }
}
