import 'package:flutter_modular/flutter_modular.dart';

import '../../components/custom_transitions.dart';
import 'ui/pages/att_page.dart';
import 'ui/pages/notifications_page.dart';
import 'ui/pages/offers_page.dart';
import 'ui/pages/product_details_page.dart';
import 'ui/pages/story_view_screen.dart';

class OffersModule extends Module {
  static const String initialRoute = '/';
  static const String productDetails = '/productDetails';
  static const String notifications = '/notifications';
  static const String attPage = '/attPage';
  static const String storyView = '/storyView';

  @override
  void routes(r) {
    r.child(
      initialRoute,
      child: (context) => OffersPage(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      productDetails,
      child: (context) => ProductDetailsPage(product: r.args.data),
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      notifications,
      child: (context) => const NotificationsPage(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      attPage,
      child: (context) => AttPage(isUpdateForce: r.args.data),
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      storyView,
      child: (context) => StoryViewScreen(
        orderedCategories: r.args.data['orderedCategories'],
        initialCategory: r.args.data['initialCategory'],
        category: r.args.data['categoria'],
        initialIndex: r.args.data['initialIndex'],
        onStoryComplete: r.args.data['onStoryComplete'],
      ),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.storyTransition(),
      duration: const Duration(milliseconds: 200),
    );
  }
}
