import 'package:flutter/material.dart';
import 'package:promobell/theme/color_outlet.dart';

class UpdateConsentText extends StatelessWidget {
  const UpdateConsentText({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Text.rich(
        TextSpan(
          text: 'Ao atualizar ',
          style: TextStyle(
            fontSize: 14,
            color: ColorOutlet.contentTertiary,
            fontFamily: 'Figtree',
            overflow: TextOverflow.ellipsis,
          ),
          children: [
            TextSpan(
              text: 'Promobell',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            TextSpan(text: ', você concorda com os nossos '),
            TextSpan(
              text: 'Termos de Uso',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            TextSpan(text: ' e '),
            TextSpan(
              text: 'Política de Privacidade.',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }
}
