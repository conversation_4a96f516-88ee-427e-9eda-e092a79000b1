import 'dart:io';

import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdateAppButton extends StatelessWidget {
  const UpdateAppButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorOutlet.surface,
        foregroundColor: ColorOutlet.contentTertiary,
        minimumSize: const Size(double.infinity, 44),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: ColorOutlet.contentDisabled,
            width: 1,
          ),
        ),
      ),
      onPressed: () => redirectToAppStore(),
      child: TextPattern.customText(
        text: 'Atualizar agora',
        fontSize: 14,
        color: ColorOutlet.contentSecondary,
      ),
    );
  }
}

void redirectToAppStore() async {
  if (Platform.isAndroid) {
    await launchUrl(
      Uri.parse(
        'https://play.google.com/store/apps/details?id=br.com.promobell',
      ),
    );
  } else {
    await launchUrl(
      Uri.parse(
        'https://apps.apple.com/br/app/promobell/id6747127292',
      ),
    );
  }
}
