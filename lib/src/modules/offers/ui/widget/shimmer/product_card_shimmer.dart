import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../../../theme/color_outlet.dart';

class ProductCardShimmer extends StatelessWidget {
  const ProductCardShimmer({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        color: ColorOutlet.paper,
      ),
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Row(
            children: [
              Shimmer.fromColors(
                baseColor: ColorOutlet.surface,
                highlightColor: ColorOutlet.backgroundContainer,
                child: Container(
                  height: 120,
                  width: 120,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: ColorOutlet.surface,
                  ),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Shimmer.fromColors(
                      baseColor: ColorOutlet.surface,
                      highlightColor: ColorOutlet.backgroundContainer,
                      child: Container(
                        height: 16,
                        width: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorOutlet.surface,
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                    Shimmer.fromColors(
                      baseColor: ColorOutlet.surface,
                      highlightColor: ColorOutlet.backgroundContainer,
                      child: Container(
                        height: 20,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorOutlet.surface,
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                    Shimmer.fromColors(
                      baseColor: ColorOutlet.surface,
                      highlightColor: ColorOutlet.backgroundContainer,
                      child: Container(
                        height: 14,
                        width: 80,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorOutlet.surface,
                        ),
                      ),
                    ),
                    SizedBox(height: 8),
                    Shimmer.fromColors(
                      baseColor: ColorOutlet.surface,
                      highlightColor: ColorOutlet.backgroundContainer,
                      child: Container(
                        height: 24,
                        width: 120,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ColorOutlet.surface,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Shimmer.fromColors(
                  baseColor: ColorOutlet.surface,
                  highlightColor: ColorOutlet.backgroundContainer,
                  child: Container(
                    height: 20,
                    width: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: ColorOutlet.surface,
                    ),
                  ),
                ),
                Shimmer.fromColors(
                  baseColor: ColorOutlet.surface,
                  highlightColor: ColorOutlet.backgroundContainer,
                  child: Container(
                    height: 20,
                    width: 100,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: ColorOutlet.surface,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
