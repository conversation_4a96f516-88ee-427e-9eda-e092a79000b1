import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';

class GrayScaleProductImage extends StatelessWidget {
  final String imageProduct;
  final double? height;
  final double? width;

  final Color? color;

  const GrayScaleProductImage({
    super.key,
    required this.imageProduct,
    this.height,
    this.width,
    this.color = ColorOutlet.surface,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: imageProduct,
      fit: BoxFit.none,
      height: height,
      width: width,
      colorBlendMode: BlendMode.multiply,
      color: color,
      progressIndicatorBuilder: (context, url, progress) => Center(
        child: CircularProgressIndicator(
          value: progress.progress,
          color: ColorOutlet.contentPrimary,
          strokeWidth: 1.5,
        ),
      ),
      errorWidget: (context, url, error) => Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: FittedBox(
            child: SvgPicture.asset(
              SvgIcons.archiveImageBroken,
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }
}
