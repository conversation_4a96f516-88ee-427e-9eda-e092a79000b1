import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../components/warning_message_box.dart';
import '../../../../../models/product.dart';
import '../../../../categories/ui/widgets/detail/custom_dialog.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import 'price_with_warning.dart';

class PriceWarningOverlay extends StatelessWidget {
  final Product product;
  final OffersController controller;
  final ProductDetailsController productDetailsController;
  final double sizeHeight;

  const PriceWarningOverlay({
    super.key,
    required this.product,
    required this.controller,
    required this.productDetailsController,
    required this.sizeHeight,
  });

  void _showWarningDialog(BuildContext context) {
    CustomDialog.show(
      context,
      title: 'Alerta de preço',
      message: 'Os preços e cupons exibidos no Promobell são obtidos diretamente das lojas parceiras e podem ser alterados a qualquer momento, sem aviso prévio por parte delas. Isso é ainda mais relevante para ofertas publicadas há mais de 30 dias, pois os valores podem não estar mais atualizados.\n\nRecomendamos sempre conferir o preço final e a disponibilidade diretamente no site do vendedor antes de finalizar a compra.',
      onConfirm: () => Navigator.pop(context),
      buttonOnly: true,
      textOnConfirm: 'Entendi',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: productDetailsController.isWithinLastThirtyDays(product.criadoEm) && productDetailsController.isWarningVisible,
      child: Container(
        height: sizeHeight,
        padding: EdgeInsets.only(
          left: 40,
          right: 40,
          top: productDetailsController.totalAppBarHeight - productDetailsController.paddingExtra,
        ),
        decoration: const BoxDecoration(color: Color.fromARGB(75, 0, 0, 0)),
        child: SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                height: product.cupom.trim().isEmpty || product.cupom.toLowerCase().trim() == 'sem cupom' ? 516 + productDetailsController.paddingExtra : 602 + productDetailsController.paddingExtra,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () {
                        _showWarningDialog(context);
                        productDetailsController.toggleWarningVisibility();
                      },
                      child: WarningMessageBox(
                        icon: SvgIcons.actionClose,
                        iconOnTheRight: true,
                        title: productDetailsController.productAlertDate(
                          product.criadoEm,
                        ),
                        message: 'Os preços exibidos aqui podem não corresponder aos praticados pela loja.\n\nClique e saiba mais.',
                        color: ColorOutlet.feedbackWarning,
                        onPressed: productDetailsController.toggleWarningVisibility,
                      ),
                    ),
                    Row(
                      children: [
                        TextPattern.customText(
                          text: controller.convertDoubleToString(
                            product.precoAtual,
                          ),
                          color: Colors.transparent,
                          fontSize: 24,
                          fontWeightOption: FontWeightOption.bold,
                        ),
                        PriceWithWarning(
                          productDetailsController: productDetailsController,
                          warningBox: true,
                          product: product,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: product.cupom.trim().isEmpty || product.cupom.toLowerCase().trim() == 'sem cupom' ? 84 : 164,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
