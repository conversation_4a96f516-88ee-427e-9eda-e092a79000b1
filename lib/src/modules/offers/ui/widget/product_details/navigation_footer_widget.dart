import 'dart:ui';

import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/black_and_white_filter.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../helpers/plataform_icons.dart';
import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import 'affiliate_link_button.dart';
import 'product_image_box.dart';
import 'styled_logo_container.dart';

class NavigationFooterWidget extends StatelessWidget {
  final Product product;
  final OffersController controller;
  final ProductDetailsController productDetailsController;

  const NavigationFooterWidget({
    super.key,
    required this.product,
    required this.controller,
    required this.productDetailsController,
  });

  @override
  Widget build(BuildContext context) {
    double progress = productDetailsController.scrollProgress.clamp(0.8, 1.0);
    double opacity = (progress - 0.8) / 0.2;
    double translateY =
        productDetailsController.navegationFooterWidgetTranslateY *
        (1 - opacity);

    double paddingBottom = MediaQuery.of(context).padding.bottom;

    return AnimatedOpacity(
      opacity: opacity,
      duration: Duration(milliseconds: 50),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 50),
        transform: Matrix4.translationValues(0, translateY, 0),
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: 66,
                decoration: BoxDecoration(
                  color: ColorOutlet.surface,
                  boxShadow: [
                    BoxShadow(
                      color: ColorOutlet.surface,
                      spreadRadius: 24,
                      blurRadius: 24,
                    ),
                  ],
                ),
              ),
            ),
            BlurredProductTile(
              paddingBottom: paddingBottom,
              product: product,
              controller: controller,
            ),
          ],
        ),
      ),
    );
  }
}

class BlurredProductTile extends StatelessWidget {
  const BlurredProductTile({
    super.key,
    required this.paddingBottom,
    required this.product,
    required this.controller,
  });

  final double paddingBottom;
  final Product product;
  final OffersController controller;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          left: 16,
          right: 16,
          bottom: 0.0,
          child: Container(
            height: 20.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.vertical(bottom: Radius.circular(24)),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF313152).withValues(alpha: 0.1),
                  blurRadius: 16.0,
                  spreadRadius: 0.0,
                  offset: const Offset(0, 16),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(left: 16, right: 16, bottom: paddingBottom),
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(24)),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 16.0, sigmaY: 16.0),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  color: ColorOutlet.paper.withValues(alpha: 0.8),
                  border: Border.all(color: ColorOutlet.paper, width: 1.0),
                ),
                child: Row(
                  spacing: 12,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: ColorOutlet.systemBorderDisabled,
                              width: 0.5,
                            ),
                          ),
                          child: product.invalidProduct
                              ? BlackAndWhiteFilter(
                                  child: ProductImageBox(
                                    radius: 12,
                                    width: 56,
                                    height: 56,
                                    product: product,
                                  ),
                                )
                              : ProductImageBox(
                                  radius: 12,
                                  width: 56,
                                  height: 56,
                                  product: product,
                                ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 4, top: 4),
                          child: StyledLogoContainer(
                            padding: 4,
                            borderRadius: 8,
                            logo: PlatformIcons.fromName(product.plataforma),
                            plataformName: product.plataforma,
                            height: 24,
                            width: 24,
                          ),
                        ),
                      ],
                    ),
                    Flexible(
                      flex: 2,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          TextPattern.customText(
                            text: product.titulo,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            fontSize: 12,
                            fontWeightOption: FontWeightOption.regular,
                          ),
                          TextPattern.customText(
                            text: controller.convertDoubleToString(
                              product.precoAtual,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            color: ColorOutlet.contentPrimary,
                            fontSize: 14,
                            fontWeightOption: FontWeightOption.semiBold,
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      flex: 3,
                      child: AffiliateLinkButton(
                        product: product,
                        controller: controller,
                        urlAfiliado: product.urlAfiliado,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
