import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/warning_message_box.dart';
import '../../../../../models/product.dart';
import '../../../controllers/products_details_controller.dart';

class PriceWithWarning extends StatelessWidget {
  final Product product;
  final bool warningBox;
  final ProductDetailsController productDetailsController;

  const PriceWithWarning({
    super.key,
    required this.product,
    this.warningBox = false,
    required this.productDetailsController,
  });

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: productDetailsController.isWithinLastThirtyDays(
        product.criadoEm,
      ),
      child: Expanded(
        child: Padding(
          padding: const EdgeInsets.only(left: 32),
          child: InkWell(
            onTap: () => productDetailsController.showWarningWithResetScroll(),
            child: WarningMessageBox(
              noMessage: true,
              icon: SvgIcons.feedbackWarning,
              title: 'Alerta de preço diferente',
              color: ColorOutlet.feedbackWarning,
              paddingHorizontal: 8,
              paddingVertical: 4,
              radius: 8,
              fontSize: 12,
              iconSize: 16,
            ),
          ),
        ),
      ),
    );
  }
}
