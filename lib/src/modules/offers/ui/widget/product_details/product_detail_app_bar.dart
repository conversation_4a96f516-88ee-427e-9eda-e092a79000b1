import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';
import '../../../controllers/products_details_controller.dart';
import 'product_overview_container.dart';

class ProductDetailAppBar extends StatefulWidget {
  final Product product;
  final Color color;
  final ProductDetailsController productDetailsController;
  final OffersController controller;

  const ProductDetailAppBar({
    super.key,
    required this.product,
    required this.color,
    required this.controller,
    required this.productDetailsController,
  });

  @override
  State<ProductDetailAppBar> createState() => _CategoryDetailAppBarState();
}

class _CategoryDetailAppBarState extends State<ProductDetailAppBar> with SingleTickerProviderStateMixin {
  final OffersController controller = Modular.get<OffersController>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return SliverAppBar(
          pinned: true,
          titleSpacing: 0,
          automaticallyImplyLeading: false,
          scrolledUnderElevation: 0,
          backgroundColor: widget.productDetailsController.scrollProgress > 0.99 ? widget.color : Colors.transparent,
          collapsedHeight: widget.productDetailsController.collapsedHeight,
          expandedHeight: widget.productDetailsController.expandedHeight,
          flexibleSpace: ProductOverviewContainer(
            productDetailsController: widget.productDetailsController,
            controller: widget.controller,
            product: widget.product,
          ),
        );
      },
    );
  }
}
