import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import 'package:promobell/src/modules/offers/controllers/products_details_controller.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/report_problem_modal.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../categories/ui/widgets/detail/custom_dialog.dart';
import 'styled_logo_container.dart';

class PlatformRow extends StatelessWidget {
  final String criadoEm;
  final String logo;
  final String plataformName;
  final int idProduto;
  final bool isStory;

  const PlatformRow({
    super.key,
    required this.logo,
    required this.criadoEm,
    required this.plataformName,
    required this.idProduto,
    this.isStory = false,
  });

  @override
  Widget build(BuildContext context) {
    final OffersController controller = Modular.get<OffersController>();
    final ProductDetailsController productDetailsController =
        Modular.get<ProductDetailsController>();
    return Stack(
      children: [
        Row(
          children: [
            StyledLogoContainer(logo: logo, plataformName: plataformName),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      TextPattern.customText(
                        text: 'Oferta',
                        fontSize: 12,
                        color: ColorOutlet.contentGhost,
                      ),
                      Spacer(),
                      Visibility(
                        visible: !isStory,
                        child: TextPattern.customText(
                          text: criadoEm,
                          fontSize: 12,
                          color: ColorOutlet.contentGhost,
                        ),
                      ),
                      const SizedBox(width: 32),
                    ],
                  ),
                  TextPattern.customText(
                    text: plataformName,
                    fontSize: 14,
                    fontWeightOption: FontWeightOption.semiBold,
                    color: ColorOutlet.contentSecondary,
                  ),
                ],
              ),
            ),
          ],
        ),
        Visibility(
          visible: !isStory,
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Stack(
              alignment: Alignment.topRight,
              children: [
                Align(
                  alignment: Alignment.topRight,
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(24),
                      child: Center(
                        child: SvgPicture.asset(
                          SvgIcons.dotsVerticalProducts,
                          height: 14,
                        ),
                      ),
                      onTap: () {
                        if (!controller.podeReportar(idProduto.toString())) {
                          CustomDialog.show(
                            context,
                            textOnConfirm: 'Entendi',
                            onConfirm: () => Modular.to.pop(),
                            onCancel: () {},
                            buttonOnly: true,
                            title: 'Ação suspensa',
                            message:
                                'Detectamos um número incomum de tentativas repetidas.\n\nPara garantir a integridade da plataforma, esta ação foi temporariamente suspensa para sua conta. \n\nVocê poderá realizar esta ação novamente em 72 horas.',
                          );
                          return;
                        }

                        if (controller.jaReportouProduto(
                          idProduto.toString(),
                        )) {
                          CustomDialog.show(
                            context,
                            textOnConfirm: 'Entendi',
                            onConfirm: () => Modular.to.pop(),
                            onCancel: () {},
                            buttonOnly: true,
                            title: 'Report já enviado',
                            message:
                                'Seu relato sobre este produto já foi enviado com sucesso!\n\nNossa equipe já está analisando e trabalhando para resolver o problema. Agradecemos sua colaboração!',
                          );
                          return;
                        }

                        showReportProblemModal(
                          context,
                          idProduto: idProduto,
                          productDetailsController: productDetailsController,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

void showReportProblemModal(
  BuildContext context, {
  required int idProduto,
  required ProductDetailsController productDetailsController,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: ColorOutlet.paper,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
    ),
    builder: (context) {
      return ReportProblemModal(
        idProduto: idProduto,
        emCimaOuEmbaixo: productDetailsController.scrollProgress > 0.99
            ? false
            : true,
      );
    },
  );
}
