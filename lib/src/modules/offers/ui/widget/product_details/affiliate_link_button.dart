import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';

class AffiliateLinkButton extends StatelessWidget {
  final OffersController controller;
  final String urlAfiliado;
  final Product product;

  const AffiliateLinkButton({
    super.key,
    required this.controller,
    required this.urlAfiliado,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 44,
      child: ElevatedButton(
        onPressed: () {
          controller.launchProductUrl(urlAfiliado);
          controller.saveClickProduct(produtoId: product.id);
        },
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(horizontal: 16),
          backgroundColor: product.invalidProduct ? ColorOutlet.contentGhost : ColorOutlet.contentPrimary,
          overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              child: TextPattern.customText(
                text: 'Ver na loja',
                color: Colors.white,
                fontSize: 14,
                fontWeightOption: FontWeightOption.regular,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 8),
            SvgPicture.asset(
              SvgIcons.arrowLink,
              colorFilter: ColorFilter.mode(Colors.white, BlendMode.srcIn),
            ),
          ],
        ),
      ),
    );
  }
}
