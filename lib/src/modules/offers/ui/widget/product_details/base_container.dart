import 'package:flutter/material.dart';

class BaseContainer extends StatelessWidget {
  final double height;
  final double width;
  final Widget? child;
  final Color? colorOpacity;
  final Color? color;
  final Color? colorBorder;

  const BaseContainer({
    this.colorBorder = Colors.transparent,
    this.color = Colors.white,
    this.colorOpacity = Colors.transparent,
    this.child,
    super.key,
    required this.width,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: colorBorder!,
          width: 1,
        ),
        color: color,
        boxShadow: [
          BoxShadow(
            color: colorOpacity!,
            spreadRadius: 2,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}
