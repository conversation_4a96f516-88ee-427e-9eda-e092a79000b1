import 'package:flutter/material.dart';

import '../../../../../components/black_and_white_filter.dart';
import '../../../../../components/flag_disabled.dart';
import '../../../../../models/product.dart';
import 'product_image_box.dart';

class BlurredUnavailableBanner extends StatelessWidget {
  const BlurredUnavailableBanner({super.key, required this.product});

  final Product product;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        BlackAndWhiteFilter(child: ProductImageBox(product: product)),
        FlagDisabled(),
      ],
    );
  }
}
