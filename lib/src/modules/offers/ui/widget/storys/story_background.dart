import 'package:flutter/material.dart';

import '../../../../../models/story_model.dart';
import '../../../controllers/story/story_animation_controller.dart';
import 'story_background_gradient.dart';
import 'story_background_image.dart';
import 'story_blur_overlay.dart';

class StoryBackground extends StatelessWidget {
  const StoryBackground({
    super.key,
    required this.storyAnimationController,
    required this.currentStory,
  });

  final StoryAnimationController storyAnimationController;
  final StoryModel currentStory;

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      opacity: storyAnimationController.isTransitioning
          ? 0.0
          : (1 -
                (storyAnimationController.dragDistance /
                        storyAnimationController.dragThreshold)
                    .clamp(0.0, 1.0)),
      duration: const Duration(milliseconds: 10),
      child: Stack(
        children: [
          if (storyAnimationController.isPageFullyLoaded)
            StoryBackgroundImage(currentStoryUrl: currentStory.urlImagem),
          if (storyAnimationController.isPageFullyLoaded) StoryBlurOverlay(),
          StoryBackgroundGradient(),
        ],
      ),
    );
  }
}
