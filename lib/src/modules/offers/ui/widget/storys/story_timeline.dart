import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../pages/story_view_screen.dart';

class StoryTimeline extends StatelessWidget {
  final int totalStories;
  final int currentIndex;
  final double progress;

  const StoryTimeline({
    super.key,
    required this.totalStories,
    required this.currentIndex,
    required this.progress,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: List.generate(totalStories, (index) {
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2),
            child: Stack(
              children: [
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: filtroCor(
                      ColorOutlet.contentGhost,
                      Color(0xFFD8D8E0),
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                if (index == currentIndex)
                  LayoutBuilder(
                    builder: (context, constraints) {
                      return Container(
                        height: 4,
                        width: constraints.maxWidth * progress,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: Colors.white,
                        ),
                      );
                    },
                  )
                else if (index < currentIndex)
                  Container(
                    height: 4,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Colors.white,
                    ),
                  ),
              ],
            ),
          ),
        );
      }),
    );
  }
}
