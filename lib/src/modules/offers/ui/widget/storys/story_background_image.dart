import 'package:flutter/material.dart';
import '../../../../../../theme/color_outlet.dart';

class StoryBackgroundImage extends StatelessWidget {
  const StoryBackgroundImage({super.key, required this.currentStoryUrl});

  final String currentStoryUrl;

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: Transform.scale(
        scale: 2,
        child: Image.network(
          currentStoryUrl,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) =>
              Container(color: ColorOutlet.surface),
        ),
      ),
    );
  }
}
