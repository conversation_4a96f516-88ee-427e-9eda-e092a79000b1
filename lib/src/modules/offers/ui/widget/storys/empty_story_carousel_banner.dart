import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import 'double_border_icon.dart';

class EmptyStoryCarouselBanner extends StatelessWidget {
  const EmptyStoryCarouselBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SizedB<PERSON>(
        height: 128,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            SizedBox(
              height: 80,
              child: Row(
                children: [
                  DoubleBorderIcon(
                    borderColor: ColorOutlet.surface,
                    empty: true,
                    child: SvgPicture.asset(
                      SvgIcons.actionNotification,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextPattern.customText(
                          text: 'Siga e economize',
                          fontSize: 24,
                          fontWeightOption: FontWeightOption.bold,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: 8),
                        TextPattern.customText(
                          text:
                              'Siga categorias com ofertas diárias e \neconomize no que você mais curte.',
                          maxLines: 2,
                          fontSize: 14,
                          fontWeightOption: FontWeightOption.regular,
                          color: ColorOutlet.contentGhost,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}
