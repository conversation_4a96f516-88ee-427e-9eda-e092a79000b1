import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';

class DottedLine extends StatelessWidget {
  final Color? color;
  const DottedLine({super.key, this.color});

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(MediaQuery.of(context).size.width, 1),
      painter: <PERSON><PERSON><PERSON><PERSON><PERSON>ain<PERSON>(
        color: color,
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color? color;

  DashedLinePainter({super.repaint, required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color =
          color ??
          ColorOutlet
              .systemBorderDisabled // Cor da linha tracejada
      ..strokeWidth =
          1 // Largura da linha
      ..strokeCap = StrokeCap.round;

    const double dashWidth = 2; // Comprimento do traço
    const double dashSpace = 2; // Espaço entre os traços

    double currentX = 0;

    while (currentX < size.width) {
      canvas.drawLine(
        Offset(currentX, 0),
        Offset(currentX + dashWidth, 0),
        paint,
      );
      currentX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
