import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/filters_modal.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../controllers/offers_controller.dart';

// xcrun simctl openurl booted "https://promobell.com.br/product?id=18634"

class FilterProductsWidget extends StatefulWidget {
  final bool? isFilterForCategory;
  final bool isPinned;
  final bool isCategoryFilters;
  final int? intCategoria;

  const FilterProductsWidget({
    required this.isCategoryFilters,
    this.isFilterForCategory = false,
    this.isPinned = false,
    this.intCategoria,
    super.key,
  });

  @override
  State<FilterProductsWidget> createState() => _FilterProductsWidgetState();
}

class _FilterProductsWidgetState extends State<FilterProductsWidget> {
  final OffersController controller = Modular.get<OffersController>();
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    controller.addListener(() {
      final selectedFilter = widget.isFilterForCategory == true
          ? controller.selectedFilterCategorias
          : controller.selectedFilter;
      _scrollToSelectedFilter(selectedFilter);
    });
  }

  void _scrollToSelectedFilter(String selectedFilter) {
    final filters = ['Recentes', 'Tem cupom', 'Com frete grátis'];
    final index = filters.indexOf(selectedFilter);
    if (index == -1 || !_scrollController.hasClients) return;

    const itemWidth = 118.0;
    const spacing = 8.0;

    final totalBefore = index * (itemWidth + spacing);
    final screenWidth = MediaQuery.of(context).size.width;
    final scrollOffset = totalBefore - (screenWidth / 2 - itemWidth / 2);

    final maxExtent = _scrollController.position.maxScrollExtent;
    final clampedOffset = scrollOffset.clamp(0.0, maxExtent);

    _scrollController.animateTo(
      clampedOffset,
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: AnimatedBuilder(
        animation: controller,
        builder: (context, child) {
          final selectedFilter = widget.isFilterForCategory == true
              ? controller.selectedFilterCategorias
              : controller.selectedFilter;

          return LayoutBuilder(
            builder: (context, constraints) {
              final availableWidth = constraints.maxWidth;
              final double defaultPadding = 24;
              final double minPadding = 4;
              final double requiredWidth = 406;
              double effectivePadding = defaultPadding;
              if (availableWidth - (2 * defaultPadding) < requiredWidth) {
                effectivePadding = ((availableWidth - requiredWidth) / 2).clamp(
                  minPadding,
                  defaultPadding,
                );
              }

              return Stack(
                children: [
                  Align(
                    alignment: Alignment.bottomLeft,
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      child: Container(
                        height: 64,
                        padding: EdgeInsets.symmetric(
                          horizontal: effectivePadding,
                        ),
                        child: Row(
                          children: [
                            FilterButton(
                              label: 'Recentes',
                              selectedFilter: selectedFilter,
                              isFilterForCategory: widget.isFilterForCategory!,
                              controller: controller,
                            ),
                            FilterButton(
                              label: 'Tem cupom',
                              selectedFilter: selectedFilter,
                              isFilterForCategory: widget.isFilterForCategory!,
                              controller: controller,
                            ),
                            FilterButton(
                              label: 'Com frete grátis',
                              selectedFilter: selectedFilter,
                              isFilterForCategory: widget.isFilterForCategory!,
                              controller: controller,
                            ),
                            FiltersWidget(
                              isCategoryFilters:
                                  widget.isCategoryFilters == true &&
                                  widget.intCategoria != null,
                              intCategoria: widget.intCategoria,
                              isSelected: controller.filtrosAtivos > 0,
                              fastFilterActive:
                                  controller.tipoListaAtual !=
                                  TipoListaProdutos.geral,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (widget.isPinned)
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        height: 1,
                        width: double.infinity,
                        color: ColorOutlet.systemBorderDisabled,
                      ),
                    ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class FilterButton extends StatelessWidget {
  final String label;
  final String selectedFilter;
  final bool isFilterForCategory;
  final OffersController controller;

  const FilterButton({
    super.key,
    required this.label,
    required this.selectedFilter,
    required this.isFilterForCategory,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final isSelected = label == selectedFilter;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: isSelected
          ? BoxDecoration(
              color: ColorOutlet.systemBorderDisabled.withAlpha(80),
              borderRadius: BorderRadius.circular(16),
            )
          : null,
      child: TextButton(
        onPressed: () {
          if (isFilterForCategory == true) {
            controller.setFilterCategory(label);
          } else {
            controller.setFilter(label);
          }
        },
        style: TextButton.styleFrom(
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: Row(
          children: [
            SvgPicture.asset(
              label == 'Recentes'
                  ? SvgIcons.arrowRefresh
                  : label == 'Tem cupom'
                  ? SvgIcons.financeCoupon
                  : SvgIcons.markerShipping,
              colorFilter: ColorFilter.mode(
                isSelected
                    ? ColorOutlet.contentPrimary
                    : ColorOutlet.contentSecondary,
                BlendMode.srcIn,
              ),
              width: 20,
              height: 20,
            ),
            const SizedBox(width: 4),
            TextPattern.customText(
              text: label,
              color: isSelected
                  ? ColorOutlet.contentPrimary
                  : ColorOutlet.contentSecondary,
              fontSize: 14,
              fontWeightOption: isSelected
                  ? FontWeightOption.semiBold
                  : FontWeightOption.regular,
            ),
          ],
        ),
      ),
    );
  }
}

class FiltersWidget extends StatelessWidget {
  final bool isCategoryFilters;
  final int? intCategoria;
  final bool isSelected;
  final bool fastFilterActive;
  const FiltersWidget({
    super.key,
    required this.isCategoryFilters,
    this.intCategoria,
    required this.isSelected,
    required this.fastFilterActive,
  });

  @override
  Widget build(BuildContext context) {
    final OffersController controller = Modular.get<OffersController>();

    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return TextButton(
          onPressed: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: Colors.transparent,
              builder: (context) {
                return FiltersModal(
                  isCategoryFilters: isCategoryFilters,
                  intCategoria: intCategoria,
                );
              },
            );
          },
          style: TextButton.styleFrom(
            elevation: 0,
            padding: EdgeInsets.only(
              right: controller.filtrosAtivos > 0 ? 8 : 16,
            ),
          ),
          child: Container(
            decoration: isSelected && fastFilterActive
                ? BoxDecoration(
                    color: ColorOutlet.systemBorderDisabled.withAlpha(80),
                    borderRadius: BorderRadius.circular(12),
                  )
                : null,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 3,
              ),
              child: Row(
                children: [
                  Stack(
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            height: 32,
                            width: 32,
                            child: Padding(
                              padding: const EdgeInsets.all(4.0),
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    SvgIcons.actionFilterAdjust,
                                    height: 20,
                                    colorFilter: ColorFilter.mode(
                                      isSelected && fastFilterActive
                                          ? ColorOutlet.contentPrimary
                                          : ColorOutlet.contentSecondary,

                                      BlendMode.srcIn,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          TextPattern.customText(
                            text: "Filtros",
                            color: isSelected && fastFilterActive
                                ? ColorOutlet.contentPrimary
                                : ColorOutlet.contentSecondary,
                            fontSize: 14,
                          ),

                          if (controller.filtrosAtivos > 0) ...[
                            Padding(
                              padding: const EdgeInsets.only(left: 4),
                              child: Container(
                                height: 14,
                                width: 14,
                                decoration: BoxDecoration(
                                  color: ColorOutlet.contentPrimary,
                                  borderRadius: BorderRadius.circular(
                                    4,
                                  ),
                                ),
                                child: Center(
                                  child: TextPattern.customText(
                                    text: controller.filtrosAtivos.toString(),
                                    fontSize: 11,
                                    color: ColorOutlet.contentTertiary,
                                    fontWeightOption: FontWeightOption.semiBold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
