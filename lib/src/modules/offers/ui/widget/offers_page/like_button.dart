import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';

class LikeButton extends StatefulWidget {
  final bool isLiked;
  final VoidCallback onTap;
  final bool bigIcon;

  const LikeButton({
    super.key,
    required this.isLiked,
    required this.onTap,
    this.bigIcon = false,
  });

  @override
  State<LikeButton> createState() => _LikeButtonState();
}

class _LikeButtonState extends State<LikeButton> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  late final AnimationController _lottieController;
  bool _shouldShowLottie = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween(
          begin: 1.0,
          end: 1.2,
        ).chain(CurveTween(curve: Curves.easeOut)),
        weight: 50,
      ),
      TweenSequenceItem(
        tween: Tween(
          begin: 1.2,
          end: 1.0,
        ).chain(CurveTween(curve: Curves.easeIn)),
        weight: 50,
      ),
    ]).animate(_controller);

    _lottieController = AnimationController(vsync: this);
  }

  @override
  void didUpdateWidget(covariant LikeButton oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Removemos toda a lógica de animação daqui
    // As animações agora são disparadas apenas pelo toque do usuário
  }

  void _handleTap() {
    // Dispara a animação de escala sempre que o usuário toca
    _controller.forward(from: 0.0);

    // Se o produto não estava curtido e agora está, mostra o Lottie
    if (!widget.isLiked) {
      // Aguarda um frame para garantir que o estado foi atualizado
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && widget.isLiked) {
          setState(() => _shouldShowLottie = true);
          _lottieController.reset();
        }
      });
    }

    // Chama o callback original
    widget.onTap();
  }

  @override
  void dispose() {
    _controller.dispose();
    _lottieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double iconSize = widget.bigIcon ? 32 : 24;
    final double containerSize = iconSize + 12; // botão fixo

    return GestureDetector(
      onTap: _handleTap,
      child: SizedBox(
        width: containerSize,
        height: containerSize,
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (_shouldShowLottie)
              Visibility(
                visible: _shouldShowLottie && widget.isLiked,
                maintainState: false,
                maintainAnimation: false,
                child: OverflowBox(
                  maxWidth: iconSize + 12,
                  maxHeight: iconSize + 12,
                  child: Lottie.asset(
                    'assets/lottie/favorite.json',
                    controller: _lottieController,
                    fit: BoxFit.contain,
                    onLoaded: (composition) {
                      _lottieController.duration = composition.duration;
                      _lottieController.forward().whenComplete(() {
                        if (mounted) {
                          setState(() => _shouldShowLottie = false);
                        }
                      });
                    },
                  ),
                ),
              ),
            AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: SvgPicture.asset(
                      widget.isLiked ? SvgIcons.markerFavoriteFilled : SvgIcons.markerFavorite,
                      colorFilter: ColorFilter.mode(
                        widget.isLiked ? const Color(0xFFFF4060) : ColorOutlet.contentSecondary,
                        BlendMode.srcIn,
                      ),
                      height: iconSize,
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
