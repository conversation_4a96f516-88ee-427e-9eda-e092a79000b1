import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';
import 'package:lottie/lottie.dart';
import 'package:promobell/src/components/black_and_white_filter.dart';
import 'package:promobell/src/components/flag_disabled.dart';
import 'package:promobell/src/helpers/open_login_modal.dart';
import 'package:promobell/src/modules/offers/offers_module.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/benifit_card.dart';
import 'package:promobell/src/modules/offers/ui/widget/offers_page/like_button.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/product_image_box.dart';
import 'package:promobell/src/modules/offers/ui/widget/product_details/styled_logo_container.dart' show StyledLogoContainer;
import 'package:promobell/theme/color_outlet.dart' show ColorOutlet;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../helpers/plataform_icons.dart';
import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';

class CardSaveProduct extends StatefulWidget {
  final Product product;
  final VoidCallback onRemoveConfirmed;

  const CardSaveProduct({
    super.key,
    required this.product,
    required this.onRemoveConfirmed,
  });

  @override
  State<CardSaveProduct> createState() => CardSaveProductState();
}

class CardSaveProductState extends State<CardSaveProduct> with TickerProviderStateMixin {
  final OffersController controller = Modular.get<OffersController>();

  late AnimationController _heartAnimationController;
  late Animation<double> _heartScaleAnimation;
  late AnimationController _lottieController;

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  bool showHeart = false;
  bool _shouldShowLottie = false;
  bool isRemoving = false;

  @override
  void initState() {
    super.initState();

    _heartAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _heartScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).chain(CurveTween(curve: Curves.easeOut)).animate(_heartAnimationController);

    _lottieController = AnimationController(vsync: this);

    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _slideAnimation =
        Tween<Offset>(
          begin: Offset.zero,
          end: const Offset(-1.5, 0),
        ).animate(
          CurvedAnimation(
            parent: _slideController,
            curve: Curves.easeInOut,
          ),
        );

    _initLikeState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        precacheImage(
          NetworkImage(widget.product.urlImagem),
          context,
        );
      }
    });
  }

  @override
  void dispose() {
    _heartAnimationController.dispose();
    _lottieController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _initLikeState() async {
    final userEmail = Supabase.instance.client.auth.currentUser?.email ?? '';
    await controller.fetchLikesCount(widget.product.id);
    await controller.fetchLikeStatus(widget.product.id, userEmail);
  }

  void _toggleLike() {
    final userEmail = Supabase.instance.client.auth.currentUser?.email ?? '';
    controller.toggleLike(widget.product.id, userEmail);
  }

  void _handleDoubleTap() {
    if (mounted) {
      setState(() {
        showHeart = true;
        _shouldShowLottie = true;
      });
    }

    if (!controller.isProductLiked(widget.product.id)) {
      _toggleLike();
    }

    // Inicia a animação de escala do SVG
    _heartAnimationController.forward(from: 0.0).then((_) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted) setState(() => showHeart = false);
      });
    });

    // Inicia a animação do Lottie
    _lottieController.reset();
  }

  Future<void> startRemoveAnimation() async {
    if (isRemoving) return;
    setState(() => isRemoving = true);
    await _slideController.forward();
    widget.onRemoveConfirmed(); // só aqui é removido
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: InkWell(
        splashColor: ColorOutlet.systemBorderDisabled.withValues(
          alpha: 0.3,
        ),
        highlightColor: ColorOutlet.systemBorderDisabled.withValues(
          alpha: 0.3,
        ),
        borderRadius: BorderRadius.circular(24),
        onTap: () {
          Modular.to.pushNamed(
            '/offers${OffersModule.productDetails}',
            arguments: widget.product,
          );
        },
        onDoubleTap: () {
          if (Supabase.instance.client.auth.currentUser == null) {
            openLoginModal(context, text: 'Promolover, entre para curtir');
            return;
          } else {
            _handleDoubleTap();
          }
        },
        child: AnimatedBuilder(
          animation: controller,
          builder: (context, child) {
            return Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24),
                    color: ColorOutlet.paper,
                  ),
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Stack(
                            children: [
                              widget.product.invalidProduct
                                  ? Stack(
                                      children: [
                                        BlackAndWhiteFilter(
                                          child: ProductImageBox(
                                            product: widget.product,
                                            height: 120,
                                            width: 120,
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                            top: 44,
                                          ),
                                          child: FlagDisabled(
                                            height: 32,
                                            text: 'Indisponível',
                                            width: 120,
                                            fontWeightOption: FontWeightOption.medium,
                                          ),
                                        ),
                                      ],
                                    )
                                  : ProductImageBox(
                                      product: widget.product,
                                      height: 120,
                                      width: 120,
                                    ),
                              Positioned(
                                top: 8,
                                left: 8,
                                child: StyledLogoContainer(
                                  padding: 4,
                                  borderRadius: 8,
                                  logo: PlatformIcons.fromName(
                                    widget.product.plataforma,
                                  ),
                                  plataformName: widget.product.plataforma,
                                  height: 24,
                                  width: 24,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    BenefitCard(
                                      ofertaIndisponivel: widget.product.invalidProduct,
                                      cupom: widget.product.cupom,
                                      frete: widget.product.frete,
                                      menorPreco: widget.product.menorPreco,
                                      plataforma: widget.product.plataforma,
                                    ),
                                    const Spacer(),
                                    TextPattern.customText(
                                      text: controller.getTimeToNow(
                                        widget.product.criadoEm,
                                      ),
                                      color: Colors.grey,
                                      fontSize: 12,
                                      fontWeightOption: FontWeightOption.regular,
                                    ),
                                    const SizedBox(width: 8),
                                    InkWell(
                                      onTap: startRemoveAnimation,
                                      child: SvgPicture.asset(
                                        SvgIcons.markerBookmarkFilled,
                                        colorFilter: ColorFilter.mode(
                                          ColorOutlet.contentSecondary,
                                          BlendMode.srcIn,
                                        ),
                                        height: 24,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                TextPattern.customText(
                                  text: widget.product.titulo,
                                  color: ColorOutlet.contentSecondary,
                                  fontSize: 14,
                                  fontWeightOption: FontWeightOption.medium,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 8),
                                Visibility(
                                  visible: controller.shouldShowOldPrice(widget.product),
                                  replacement: const SizedBox(
                                    height: 20,
                                  ),
                                  child: TextPattern.customText(
                                    text: controller.convertDoubleToString(
                                      widget.product.precoAntigo,
                                    ),
                                    fontSize: 14,
                                    fontWeightOption: FontWeightOption.regular,
                                    color: ColorOutlet.contentGhost,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    decoration: TextDecoration.lineThrough,
                                    decorationColor: ColorOutlet.contentGhost,
                                  ),
                                ),
                                TextPattern.customText(
                                  text: controller.convertDoubleToString(
                                    widget.product.precoAtual,
                                  ),
                                  color: ColorOutlet.contentPrimary,
                                  fontSize: 20,
                                  fontWeightOption: FontWeightOption.bold,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Transform.translate(
                        offset: Offset(-4, 0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                LikeButton(
                                  isLiked: controller.isProductLiked(
                                    widget.product.id,
                                  ),

                                  onTap: () {
                                    if (Supabase.instance.client.auth.currentUser == null) {
                                      openLoginModal(context, text: 'Promolover, entre para curtir');
                                      return;
                                    } else {
                                      _toggleLike();
                                    }
                                  },
                                ),
                                TextPattern.customText(
                                  text: controller
                                      .getLikesCount(
                                        widget.product.id,
                                      )
                                      .toString(),
                                  color: ColorOutlet.contentSecondary,
                                  fontSize: 14,
                                  fontWeightOption: FontWeightOption.regular,
                                ),
                              ],
                            ),
                            const Spacer(),
                            TextPattern.customText(
                              text: 'Conferir oferta',
                              color: ColorOutlet.contentPrimary,
                              fontSize: 14,
                              fontWeightOption: FontWeightOption.regular,
                            ),
                            const SizedBox(width: 4),
                            SvgPicture.asset(
                              SvgIcons.arrowRightTip,
                              width: 20,
                              height: 20,
                              colorFilter: ColorFilter.mode(
                                ColorOutlet.contentPrimary,
                                BlendMode.srcIn,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Animação Lottie de fundo
                if (_shouldShowLottie)
                  Visibility(
                    visible: _shouldShowLottie && controller.isProductLiked(widget.product.id),
                    child: Positioned.fill(
                      child: Center(
                        child: SizedBox(
                          width: 120,
                          height: 120,
                          child: Lottie.asset(
                            'assets/lottie/favorite.json',
                            controller: _lottieController,
                            fit: BoxFit.contain,
                            onLoaded: (composition) {
                              _lottieController.duration = composition.duration;
                              _lottieController.forward().whenComplete(() {
                                if (mounted) {
                                  setState(
                                    () => _shouldShowLottie = false,
                                  );
                                }
                              });
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                // Animação SVG de escala
                if (showHeart)
                  Positioned.fill(
                    child: Center(
                      child: ScaleTransition(
                        scale: _heartScaleAnimation,
                        child: SvgPicture.asset(
                          SvgIcons.markerFavoriteFilled,
                          colorFilter: const ColorFilter.mode(
                            Color(0xFFFF4060),
                            BlendMode.srcIn,
                          ),
                          height: 100,
                          width: 100,
                        ),
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }
}
