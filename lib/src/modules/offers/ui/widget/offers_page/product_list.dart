import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import '../../../../../models/product.dart';
import '../../../../../services/navigation/scroll_services.dart';
import '../../../offers_module.dart';
import 'product_frame_widget.dart';

class ProductList extends StatelessWidget {
  final List<Product> products;

  const ProductList({super.key, required this.products});

  @override
  Widget build(BuildContext context) {
    final scrollService = Modular.get<ScrollService>();
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: products.length,
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemBuilder: (context, index) {
        final product = products[index];
        return Padding(
          padding: EdgeInsets.only().copyWith(
            right: index == products.length - 1 ? 0 : 8,
          ),
          child: ProductFrameWidget(
            product: product,
            onPressed: () {
              scrollService.saveScrollPosition('offersPage');
              Modular.to.pushNamed(
                '/offers${OffersModule.productDetails}',
                arguments: product,
              );
            },
          ),
        );
      },
    );
  }
}
