import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../helpers/plataform_icons.dart';
import '../../../../../models/product.dart';
import '../../../controllers/offers_controller.dart';
import '../product_details/product_image_box.dart';
import '../product_details/styled_logo_container.dart';

class ProductFrameWidget extends StatelessWidget {
  final Product product;
  final VoidCallback onPressed;

  const ProductFrameWidget({
    super.key,
    required this.product,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    OffersController controller = Modular.get<OffersController>();
    return InkWell(
      splashColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
      highlightColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
      onTap: onPressed,
      child: Container(
        width: 130,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
        child: Column(
          spacing: 8,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                ProductImageBox(product: product, height: 120, width: 120),
                Positioned(
                  top: 8,
                  left: 8,
                  child: StyledLogoContainer(
                    padding: 4,
                    borderRadius: 8,
                    logo: PlatformIcons.fromName(product.plataforma),
                    plataformName: product.plataforma,
                    height: 24,
                    width: 24,
                  ),
                ),
              ],
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 40,
                  child: TextPattern.customText(
                    text: product.titulo,
                    fontSize: 14,
                    fontWeightOption: FontWeightOption.medium,
                    color: ColorOutlet.contentTertiary,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(height: 8),
                Visibility(
                  replacement: SizedBox(height: 20),
                  visible: controller.shouldShowOldPrice(product),
                  child: TextPattern.customText(
                    text: controller.convertDoubleToString(product.precoAntigo),
                    fontSize: 14,
                    fontWeightOption: FontWeightOption.medium,
                    color: ColorOutlet.contentGhost,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    decoration: TextDecoration.lineThrough,
                    decorationColor: ColorOutlet.contentGhost,
                  ),
                ),
                TextPattern.customText(
                  text: controller.convertDoubleToString(product.precoAtual),
                  fontSize: 20,
                  fontWeightOption: FontWeightOption.bold,
                  color: ColorOutlet.contentTertiary,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextPattern.customText(
                  text: 'Conferir oferta',
                  fontSize: 14,
                  fontWeightOption: FontWeightOption.regular,
                  color: ColorOutlet.contentTertiary,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SvgPicture.asset(
                  SvgIcons.arrowRightTip,
                  width: 20,
                  height: 20,
                  colorFilter: ColorFilter.mode(
                    ColorOutlet.contentTertiary,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
