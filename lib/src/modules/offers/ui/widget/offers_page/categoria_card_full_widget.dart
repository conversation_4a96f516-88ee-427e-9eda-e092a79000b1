import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../components/vidio_widget_player.dart';
import '../../../../../models/categorias_menu.dart';
import '../../../../../models/product.dart';
import 'category_header_widget.dart';
import 'product_list.dart';

class CategoriaCardFullWidget extends StatefulWidget {
  const CategoriaCardFullWidget({
    super.key,
    required this.categoria,
    this.isPrimary = false,
  });

  final CategoriaMenu categoria;
  final bool isPrimary;

  @override
  State<CategoriaCardFullWidget> createState() =>
      _CategoriaCardFullWidgetState();
}

class _CategoriaCardFullWidgetState extends State<CategoriaCardFullWidget> {
  final OffersController controller = Modular.get<OffersController>();
  List<Product> products = [];

  @override
  void initState() {
    super.initState();

    controller.carregarTopProdutos(categoria: widget.categoria.nome).then((
      produtosCarregados,
    ) {
      if (mounted) {
        setState(() {
          products = produtosCarregados;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        right: 16,
        left: 16,
        bottom: widget.isPrimary ? 0 : 16,
      ),
      child: SizedBox(
        height: widget.isPrimary ? 511 : null,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: SizedBox(
                height: 500,
                child: VideoWidget(
                  videoPath: widget.categoria.video!,
                ),
              ),
            ),
            Positioned(
              top: widget.isPrimary ? 35 : 24,
              left: 24,
              right: 24,
              child: CategoryHeaderWidget(
                categoria: widget.categoria,
              ),
            ),
            Positioned(
              bottom: 12,
              left: 0,
              right: 0,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
                child: AnimatedBuilder(
                  animation: controller,
                  builder: (context, child) {
                    return SizedBox(
                      height: 260,
                      child: ProductList(products: products),
                    );
                  },
                ),
              ),
            ),
            Visibility(
              visible: widget.isPrimary,
              child: Padding(
                padding: const EdgeInsets.only(left: 23),
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Container(
                    height: 22,
                    width: 120,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: ColorOutlet.contentSecondary,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: TextPattern.customText(
                      text: 'RECOMENDAMOS',
                      color: ColorOutlet.contentTertiary,
                      fontSize: 11,
                      fontWeightOption: FontWeightOption.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
