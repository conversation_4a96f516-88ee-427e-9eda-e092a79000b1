import 'package:flutter/material.dart';

class SearchHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeight;

  SearchHeaderDelegate({
    required this.child,
    required this.minHeight,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return child;
  }

  @override
  double get maxExtent => minHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SearchHeaderDelegate oldDelegate) {
    return child != oldDelegate.child || minHeight != oldDelegate.minHeight;
  }
}
