import 'package:flutter/material.dart';
import '../../../../../models/categorias_menu.dart';

class CategoryImageBadg extends StatelessWidget {
  final CategoriaMenu categoria;
  final double width;
  final double height;
  final double heightImage;
  final double widthImage;
  final double radius;

  const CategoryImageBadg({
    super.key,
    this.width = 48,
    this.height = 48,
    this.heightImage = 32,
    this.widthImage = 32,
    this.radius = 16,
    required this.categoria,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius),
            color: categoria.cor,
          ),
          height: height,
          width: width,
        ),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius),
            color: const Color(0xFFFFFFFF).withAlpha(100),
          ),
          height: height,
          width: width,
        ),
        Positioned(
          bottom: 0,
          right: 0, // Posiciona a imagem à direita
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              bottomRight: Radius.circular(radius),
            ),
            child: Container(
              height: heightImage,
              width: heightImage,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(categoria.fotoPequena),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
