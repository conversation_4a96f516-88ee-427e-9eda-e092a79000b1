import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../theme/color_outlet.dart';
import '../../../../components/custom_pop_scope.dart';
import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../core/base/widgets/no_connect.dart';
import '../../../profile/ui/widgets/profile/header_with_back_button.dart';
import '../../controllers/notification_controller.dart';
import '../widget/notifications_widgets/card_notification_storage.dart';
import 'empty_state_pattern_lottie.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  NotificationController controller = Modular.get<NotificationController>();
  final controllerBase = Modular.get<BaseController>();

  @override
  void initState() {
    super.initState();
    controller.deleteNotificationOld();
    controller.getNotifications();
  }

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(
      index: 0,
      child: NoConnect(
        child: Scaffold(
          backgroundColor: ColorOutlet.paper,
          body: Column(
            children: [
              HeaderWithBackButton(
                title: 'Alertas',
                isNotification: true,
                isOffers: true,
              ),
              Expanded(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                    ),
                    child: ValueListenableBuilder(
                      valueListenable: controller.notifications,
                      builder: (context, value, child) {
                        if (controller.notifications.value.isNotEmpty) {
                          return ListView.builder(
                            padding: const EdgeInsets.only(top: 16, bottom: 16),
                            itemCount: controller.notifications.value.length,
                            itemBuilder: (context, index) {
                              final notification = controller.notifications.value[controller.notifications.value.length - index - 1];
                              return Padding(
                                padding: const EdgeInsets.only(
                                  bottom: 24,
                                ),
                                child: GestureDetector(
                                  onTap: () {
                                    controller.setReadNotification(
                                      notification,
                                    );
                                  },
                                  child: CardNotificationStorage(
                                    title: notification.title ?? '',
                                    body: notification.body ?? '',
                                    date: notification.date ?? '',
                                    read: notification.isRead ?? 0,
                                    id: notification.id,
                                    idProduto: notification.idProduto,
                                    email: notification.email ?? '',
                                  ),
                                ),
                              );
                            },
                          );
                        } else {
                          return Padding(
                            padding: const EdgeInsets.only(top: 100),
                            child: EmptyStatePatternLottie(),
                          );
                        }
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
