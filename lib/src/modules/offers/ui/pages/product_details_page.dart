import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../app_links_service.dart';
import '../../../../../theme/color_outlet.dart';
import '../../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../../models/categorias_menu.dart';
import '../../../../models/product.dart';
import '../../../categories/controllers/categories_controller.dart';
import '../../../categories/ui/widgets/detail/category_background_animated.dart';
import '../../../categories/ui/widgets/detail/sliver_app_bar_delegate.dart';
import '../../controllers/offers_controller.dart';
import '../../controllers/products_details_controller.dart';
import '../widget/offers_page/animated_list_widget.dart';
import '../widget/offers_page/product_card_widget.dart';
import '../widget/product_details/navigation_footer_widget.dart';
import '../widget/product_details/price_warning_overlay.dart';
import '../widget/product_details/product_category_line.dart';
import '../widget/product_details/product_detail_app_bar.dart';
import '../widget/product_details/scroll_aware_app_bar.dart';
import '../widget/shimmer/product_card_shimmer.dart';

class ProductDetailsPage extends StatefulWidget {
  final Product product;

  const ProductDetailsPage({super.key, required this.product});

  @override
  State<ProductDetailsPage> createState() => _ProductDetailsPageState();
}

class _ProductDetailsPageState extends State<ProductDetailsPage> with SingleTickerProviderStateMixin {
  final OffersController controller = Modular.get<OffersController>();
  final ProductDetailsController productDetailsController = Modular.get<ProductDetailsController>();
  final CategoriesController controllerCategories = Modular.get<CategoriesController>();
  final BaseController controllerBase = Modular.get<BaseController>();
  double _gestureStartX = 0;
  bool _ativo = true;

  @override
  void initState() {
    super.initState();
    getProdutos();

    // Chama getProdutos apenas se a lista estiver vazia ou com menos de 10 produtos
    if (controller.produtos.isEmpty || controller.produtos.length < 10) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.fetchNextProdutos();
      });
    }

    // Chama a função com delay de forma segura
    Future.delayed(const Duration(seconds: 3), () {
      if (!_ativo || !mounted) return;

      controller.registrarVisualizacaoProduto(
        produtoId: widget.product.id,
      );
    });
  }

  @override
  void dispose() {
    _ativo = false;
    productDetailsController.dispose();
    controllerCategories.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    Future.delayed(Duration.zero, () {
      if (!mounted) return;
      productDetailsController.updateScreenMetrics(
        context,
        widget.product,
      );
      productDetailsController.resetScroll();
    });
  }

  Future<void> getProdutos() async {
    await controller.getProdutosFiltradosCategoriasInSupabase(
      categoria: widget.product.categoria,
    );
  }

  @override
  Widget build(BuildContext context) {
    final categoria = CategoriaMenu.getCategoriaByNome(
      widget.product.categoria,
    );
    final filterColor = controllerCategories.getFilteredColor(
      categoria,
    );

    final paddingBottom = MediaQuery.of(context).padding.bottom;
    final sizeHeight = MediaQuery.of(context).size.height;

    return AnimatedBuilder(
      animation: Listenable.merge([
        controller,
        productDetailsController,
      ]),
      builder: (context, _) {
        return GestureDetector(
          onHorizontalDragStart: (details) {
            _gestureStartX = details.globalPosition.dx;
          },
          onHorizontalDragUpdate: (details) {
            if (_gestureStartX < 40 && details.delta.dx > 20) {
              Modular.to.pop();
            }
          },
          child: PopScope(
            canPop: true,
            onPopInvokedWithResult: (didPop, result) async {
              if (didPop) return;

              final isFromDeepLink = AppLinksService.hasReceivedLink;

              if (isFromDeepLink) {
                // Se veio de um deeplink, usa o método especial para voltar para a home
                controllerBase.navBackFromProductDeepLink();
              } else {
                // Comportamento normal
                Modular.to.pop();
              }
            },
            child: Scaffold(
              extendBody: true,
              backgroundColor: filterColor,
              body: Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      top: productDetailsController.totalAppBarHeight + 150,
                    ),
                    child: CategoryBackgroundAnimated(
                      isDetailsProduct: true,
                      productDetailsController: productDetailsController,
                    ),
                  ),
                  Container(
                    color: productDetailsController.scrollProgress > 0.99 ? ColorOutlet.surface : Colors.transparent,
                    height: productDetailsController.screenHeight,
                  ),
                  CustomScrollView(
                    controller: productDetailsController.scrollController,
                    physics: controller.produtosPorCategoria.isEmpty ? NeverScrollableScrollPhysics() : BouncingScrollPhysics(),
                    slivers: [
                      ProductDetailAppBar(
                        product: widget.product,
                        color: filterColor,
                        productDetailsController: productDetailsController,
                        controller: controller,
                      ),
                      SliverPersistentHeader(
                        pinned: true,
                        delegate: SliverAppBarDelegate(
                          maxHeight: 64,
                          minHeight: 64,
                          child: ProductCategoryLine(
                            categoria: categoria,
                            controller: productDetailsController,
                            color: filterColor,
                          ),
                        ),
                      ),
                      SliverList(
                        delegate: SliverChildBuilderDelegate(
                          childCount: controller.produtosPorCategoria.length,
                          (context, index) {
                            final produtos = controller.produtosPorCategoria;
                            produtos.removeWhere(
                              (produto) => produto.id == widget.product.id,
                            );
                            final animationDuration = Duration(
                              milliseconds: 300 + (index * 100),
                            );
                            if (produtos.isEmpty) return Container();

                            return AnimatedListItem(
                              duration: animationDuration,
                              child: Container(
                                color: ColorOutlet.surface,
                                padding: produtos.last == produtos[index]
                                    ? EdgeInsets.only(
                                        top: 0,
                                        left: 16,
                                        right: 16,
                                        bottom: 120 + paddingBottom,
                                      )
                                    : EdgeInsets.only(
                                        top: 0,
                                        left: 16,
                                        right: 16,
                                        bottom: 16,
                                      ),
                                child: controller.loadingFilter
                                    ? ProductCardShimmer()
                                    : ProductCardWidget(
                                        product: produtos[index],
                                      ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                  ScrollAwareAppBar(
                    widget: widget,
                    productDetailsController: productDetailsController,
                  ),

                  Visibility(
                    visible: productDetailsController.scrollProgress >= 0.8,
                    child: NavigationFooterWidget(
                      product: widget.product,
                      controller: controller,
                      productDetailsController: productDetailsController,
                    ),
                  ),
                  PriceWarningOverlay(
                    product: widget.product,
                    controller: controller,
                    productDetailsController: productDetailsController,
                    sizeHeight: sizeHeight,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
