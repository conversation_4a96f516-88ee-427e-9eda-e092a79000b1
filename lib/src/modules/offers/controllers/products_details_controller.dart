import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../../models/product.dart';
import '../ui/widget/product_details/copy_coupon_button.dart';

class ProductDetailsController with ChangeNotifier {
  // Deslocamento do Scroll.
  double _scrollOffset = 0.0;
  late ScrollController _scrollController;

  // Altura da SliverAppBar expandida e contraída.
  double expandedHeight = Platform.isAndroid ? 602 + 8 : 602;
  double collapsedHeight = Platform.isAndroid ? 80 : 72;

  //Tamanhos da AppBar e SnackBar
  double topPadding = 0.0;
  double totalAppBarHeight = 0.0;
  double screenHeight = 0.0;
  double appBarHeight = 0.0;

  double warningBoxHeight = 0.0;
  // padding adiconola no And
  double paddingExtra = Platform.isAndroid ? 8 : 0;

  void updateWarningBoxHeight(double height) {
    warningBoxHeight = height;
    notifyListeners();
  }

  void updateScreenMetrics(BuildContext context, Product product) {
    final appBar = AppBar();

    topPadding = MediaQuery.of(context).padding.top + paddingExtra;
    appBarHeight = appBar.preferredSize.height;
    screenHeight = MediaQuery.of(context).size.height;

    totalAppBarHeight = topPadding + appBarHeight;

    double baseHeightWithoutCupom = 516;
    double baseHeightWithCupom = 602;

    expandedHeight = (product.cupom.trim().isEmpty || product.cupom.toLowerCase().trim() == 'sem cupom' ? baseHeightWithoutCupom : baseHeightWithCupom) + appBarHeight + warningBoxHeight;

    notifyListeners();
  }

  // Cálculo da porcentagem de rolagem.
  double get scrollProgress => (_scrollOffset / (expandedHeight - collapsedHeight)).clamp(0.0, 1.0);

  // Opacidade dos itens da SliverAppBar.
  double get opacity => scrollProgress;

  // Opacidade dos itens do ProductOverviewContainer.
  double get opacityCard => (1.0 - (scrollProgress / 1.0)).clamp(0.0, 1.0);

  // Deslocamento do background da pagina.
  double get backgroundTranslateY {
    double maxTranslateY = 165;
    return (_scrollOffset * 0.5).clamp(0.0, maxTranslateY);
  }

  double get navegationFooterWidgetTranslateY {
    double maxTranslation = 150;
    double containerTranslateY = _scrollOffset * 0.2;
    return containerTranslateY.clamp(0.0, maxTranslation);
  }

  //Deslocamento dos itens do ProductOverviewContainer
  double get columnTranslateY {
    double maxTranslationColumn = 10;
    double columnTranslateY = -_scrollOffset * 0.2;
    return columnTranslateY.clamp(-maxTranslationColumn, 0.0);
  }

  // get do scrollController.
  ScrollController get scrollController => _scrollController;

  // To this:
  ProductDetailsController() {
    _initScroll();
  }
  void _initScroll() {
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    _scrollOffset = _scrollController.offset;
    notifyListeners();
  }

  // Getter para acessar o offset do scroll
  double get scrollOffset => _scrollOffset;

  // Resetar o ScrollController quando necessário
  void resetScroll() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose(); // Libera o anterior
    _initScroll(); // Cria um novo
    notifyListeners();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  // funções do WarningMessageBox

  bool _isWarningVisible = true;

  bool get isWarningVisible => _isWarningVisible;

  void toggleWarningVisibility() {
    _isWarningVisible = !_isWarningVisible;
    notifyListeners();
  }

  void hideWarning() {
    _isWarningVisible = false;
    notifyListeners();
  }

  bool isWithinLastThirtyDays(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    return difference.inDays >= 30;
  }

  String productAlertDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference >= 90) {
      return 'Oferta postada há mais de 90 dias';
    } else if (difference >= 60) {
      return 'Oferta postada há mais de 60 dias';
    } else {
      return 'Oferta postada há mais de 30 dias';
    }
  }

  Future<void> showWarningWithResetScroll() async {
    if (_scrollController.hasClients) {
      await _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 100),
        curve: Curves.easeOut,
      );
    }

    _isWarningVisible = true;
    notifyListeners();
  }

  // ==========================================================================
  // BUTTON COUPON
  // ==========================================================================

  CouponButtonState _buttonState = CouponButtonState.copy;
  String? _copiedCouponCode;

  CouponButtonState get buttonState => _buttonState;
  String? get copiedCouponCode => _copiedCouponCode;

  void copyCoupon(String couponCode) {
    Clipboard.setData(ClipboardData(text: couponCode));
    _copiedCouponCode = couponCode;

    _buttonState = CouponButtonState.copied;
    notifyListeners();
  }
}
