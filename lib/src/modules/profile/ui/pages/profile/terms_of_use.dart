import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/custom_pop_scope.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../widgets/policy/formatted_text.dart';
import '../../widgets/policy/section_title_widget.dart';
import '../../widgets/policy/separation_line.dart';
import '../../widgets/profile/header_with_back_button.dart';

class TermsOfUse extends StatefulWidget {
  const TermsOfUse({super.key});

  @override
  State<TermsOfUse> createState() => _UsePoliticsState();
}

class _UsePoliticsState extends State<TermsOfUse> {
  final List<String> boldWords = [
    "Promobell",
    "Amazon",
    "Mercado Livre",
    "Magazine Luiza",
    "Shopee",
  ];
  final List<String> coloredWords = ["Política de Privacidade"];
  final RegExp emailRegex = RegExp(r'\b[\w\.-]+@[\w\.-]+\.\w+\b');

  final Map<String, VoidCallback> actionWords = {
    "Política de Privacidade": () =>
        Modular.to.pushNamed('/profile/privacy_politics'),
  };

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(
      index: 2,
      child: NoConnect(
        child: Scaffold(
          backgroundColor: ColorOutlet.paper,
          body: SelectionArea(
            child: Column(
              children: [
                HeaderWithBackButton(title: 'Termos de Uso'),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SectionTitle(title: 'Introdução'),
                          FormattedText(
                            text:
                                'A transparência e a segurança na utilização dos serviços oferecidos pelo Promobell são compromissos fundamentais da nossa empresa. Estes Termos de Uso estabelecem as regras e condições para o acesso e utilização do aplicativo, detalhando as responsabilidades da plataforma e dos usuários.',
                          ),
                          FormattedText(
                            text:
                                'O Promobell atua exclusivamente como um canal de divulgação de ofertas de terceiros, incluindo Mercado Livre, Shopee, Amazon e Magazine Luiza, das quais somos afiliados. O aplicativo não realiza vendas, não intermedia transações e não garante a entrega ou o recebimento de produtos adquiridos pelos usuários por meio dos links promocionais divulgados.',
                          ),
                          FormattedText(
                            text:
                                'Assim, é essencial que os usuários compreendam os limites da nossa atuação e utilizem o serviço de acordo com as disposições aqui estabelecidas.',
                          ),
                          FormattedText(
                            text:
                                'Ao acessar ou utilizar o Promobell, o usuário declara estar ciente e concorda com as condições descritas neste documento. Caso não concorde com qualquer cláusula dos Termos de Uso, recomendamos que interrompa o uso do serviço.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title:
                                '1. Independência e Isenção de Responsabilidade',
                          ),
                          FormattedText(
                            text:
                                'As ofertas exibidas no Promobell são de responsabilidade exclusiva das lojas anunciantes. Embora busquemos fornecer informações precisas e atualizadas, não garantimos a exatidão dos dados apresentados, nem nos responsabilizamos por:',
                          ),
                          FormattedText(
                            isList: true,
                            listItems: [
                              'Alterações de preços realizadas pelos anunciantes;',
                              'Disponibilidade dos produtos divulgados;',
                              'Qualidade, entrega ou quaisquer outros aspectos relacionados à compra;',
                              'Erros ou omissões nas informações fornecidas pelos lojistas.',
                            ],
                          ),
                          FormattedText(
                            text:
                                'O usuário deve verificar todas as condições diretamente na loja anunciante antes de concluir sua compra.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title: '2. Restrições de Publicação',
                          ),
                          FormattedText(
                            text:
                                'O Promobell reserva-se o direito de não divulgar e remover qualquer oferta que envolva conteúdos proibidos ou que estejam em desacordo com nossas diretrizes. São expressamente vedadas ofertas relacionadas a:',
                          ),
                          FormattedText(
                            isList: true,
                            listItems: [
                              'Armas de fogo, munições e explosivos;',
                              'Drogas e substâncias controladas;',
                              'Conteúdos adultos, pornográficos, de cunho sexual infantil ou que envolvam exploração de animais;',
                              'Venda de animais;',
                              'Produtos ou serviços que promovam discurso de ódio, violência ou discriminação;',
                              'Qualquer item que infrinja as leis vigentes no Brasil.',
                            ],
                          ),
                          FormattedText(
                            text:
                                'Caso identifiquemos qualquer violação a essas diretrizes, a oferta será removida sem aviso prévio.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title: '3. Controle e Moderação das Ofertas',
                          ),
                          FormattedText(
                            text:
                                'O Promobell se reserva o direito de editar ou remover qualquer oferta divulgada a qualquer momento, em especial nos seguintes casos:',
                          ),
                          FormattedText(
                            isList: true,
                            listItems: [
                              'Informações imprecisas ou enganosas;',
                              'Alterações de preços ou indisponibilidade do estoque;',
                              'Erros nas descrições fornecidas pela loja anunciante;',
                              'Ofertas que contrariem as diretrizes estabelecidas.',
                            ],
                          ),
                          FormattedText(
                            text:
                                'Além disso, caso o usuário identifique ofertas desatualizadas ou inconsistentes, poderá reportá-las diretamente dentro do próprio aplicativo, garantindo assim a precisão das informações exibidas.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title:
                                '4. Uso de Imagens e Propriedade Intelectual',
                          ),
                          FormattedText(
                            text:
                                'As logomarcas exibidas no Promobell pertencem exclusivamente às lojas anunciantes e seus respectivos fornecedores.',
                          ),
                          FormattedText(
                            text:
                                'O Promobell não edita, manipula ou altera imagens de produtos. Todas as imagens exibidas correspondem às disponibilizadas pelos lojistas. Algumas imagens podem ser meramente ilustrativas e são de propriedade das empresas responsáveis pelos produtos.',
                          ),
                          FormattedText(
                            text:
                                'É expressamente proibido copiar, reproduzir ou redistribuir qualquer conteúdo do aplicativo, incluindo textos, imagens e materiais promocionais, sem autorização prévia.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title: '5. Privacidade e Proteção de Dados',
                          ),
                          FormattedText(
                            text:
                                'O Promobell adota uma política rigorosa de privacidade para garantir a segurança e integridade dos dados pessoais dos usuários.',
                          ),
                          FormattedText(
                            text:
                                'Não compartilhamos informações pessoais com terceiros, salvo em situações de obrigação legal. Para mais detalhes sobre como os dados dos usuários são tratados, armazenados e protegidos, consulte nossa Política de Privacidade.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title: '6. Alterações nos termos de uso',
                          ),
                          FormattedText(
                            text:
                                'O Promobell se reserva o direito de atualizar este documento periodicamente para atender exigências legais, regulatórias ou operacionais, garantindo a melhoria contínua da experiência do usuário e da utilização do serviço.',
                          ),
                          FormattedText(
                            text:
                                'Sempre que houver alterações significativas, os usuários serão notificados via push notification e dentro do próprio aplicativo.',
                          ),
                          FormattedText(
                            text:
                                'Recomendamos que os usuários revisem periodicamente estes Termos de Uso para se manterem informados sobre eventuais atualizações.',
                          ),
                          SeparationLine(),
                          SectionTitle(
                            title: '7. Vigência e Disposições Finais',
                          ),
                          FormattedText(
                            text:
                                'A utilização do Promobell implica na leitura, compreensão e concordância com estes Termos de Uso e suas diretrizes.',
                          ),
                          FormattedText(
                            text:
                                'Se o usuário tiver qualquer dúvida sobre estes Termos de Uso ou precisar de suporte, poderá entrar em contato através do seguinte canal oficial:',
                          ),
                          FormattedText(
                            text: '<EMAIL>',
                          ),
                          FormattedText(
                            text:
                                'Nos comprometemos a responder às solicitações dentro dos prazos estabelecidos pela legislação vigente.',
                          ),
                          FormattedText(
                            text:
                                'Estes Termos de Uso entram em vigor na data de sua publicação e permanecerão válidos até que uma nova versão seja disponibilizada.',
                          ),
                          SeparationLine(),
                          const SizedBox(height: 24),
                          TextPattern.customText(
                            text: '© Promobell LTDA',
                          ),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
