import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/custom_pop_scope.dart';
import '../../../../../components/custom_snack_bar.dart';
import '../../../../../components/field_pattern.dart';
import '../../../../../components/text_pattern.dart';
import '../../../../../components/warning_message_box.dart';
import '../../../../../core/base/widgets/no_connect.dart';
import '../../../controllers/login_controller.dart';
import '../../../controllers/profile_controller.dart';
import '../../widgets/profile/header_with_back_button.dart';

class DeleteAccount extends StatefulWidget {
  const DeleteAccount({super.key});

  @override
  State<DeleteAccount> createState() => _DeleteAccountState();
}

class _DeleteAccountState extends State<DeleteAccount> {
  final LoginController controller = Modular.get<LoginController>();
  final profileController = Modular.get<ProfileController>();
  final offersController = Modular.get<OffersController>();

  List<String> reasons = [
    'Criei outra conta no Promobell',
    'Não tenho mais interesse no app',
    'As ofertas não me atendem',
    'Recebo muitas notificações',
    'Tive problemas técnicos ou bugs',
    'Tenho preocupações com privacidade',
    'Encontrei outro app melhor',
    'Outro motivo',
  ];

  @override
  Widget build(BuildContext context) {
    return CustomPopScope(
      index: 2,
      child: NoConnect(
        child: Scaffold(
          backgroundColor: ColorOutlet.paper,
          body: SelectionArea(
            child: AnimatedBuilder(
              animation: Listenable.merge([
                controller,
                profileController,
              ]),
              builder: (context, child) {
                return Column(
                  children: [
                    HeaderWithBackButton(
                      title: 'Excluir conta',
                      onBackPressed: () {
                        controller.resetDeleteAccount();
                      },
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 24,
                          ),
                          child: Column(
                            spacing: 24,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextPattern.customText(
                                text: 'Lamentamos que você esteja indo embora. Se houver algo que possamos melhorar, adoraríamos saber!',
                                fontSize: 16,
                                fontWeightOption: FontWeightOption.semiBold,
                              ),
                              Column(
                                children: reasons.map((reason) {
                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 0,
                                    ),
                                    child: ListTile(
                                      contentPadding: const EdgeInsets.only(
                                        left: 10,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(
                                          16,
                                        ),
                                      ),
                                      title: TextPattern.customText(
                                        text: reason,
                                        fontSize: 14,
                                      ),
                                      trailing: Radio<String>(
                                        value: reason,
                                        groupValue: controller.selectedReason,
                                        activeColor: ColorOutlet.contentPrimary,
                                        fillColor: WidgetStateProperty.resolveWith<Color>((
                                          Set<WidgetState> states,
                                        ) {
                                          if (states.contains(
                                            WidgetState.disabled,
                                          )) {
                                            return ColorOutlet.contentGhost;
                                          }
                                          if (states.contains(
                                            WidgetState.selected,
                                          )) {
                                            return ColorOutlet.contentPrimary;
                                          }
                                          return ColorOutlet.contentGhost;
                                        }),
                                        onChanged: (value) => controller.updateSelectedReason(
                                          value,
                                        ),
                                      ),
                                      onTap: () => controller.updateSelectedReason(
                                        reason,
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                              if (controller.selectedReason == 'Outro motivo') ...[
                                TextPattern.customText(
                                  text: 'Caso seu motivo não esteja listado acima, compartilhe conosco.',
                                  fontSize: 14,
                                  fontWeightOption: FontWeightOption.semiBold,
                                ),
                                FieldPattern(
                                  controller: controller.optionalReasonController,
                                  hintText: 'Opcional',
                                  height: 132,
                                  onChanged: (value) => controller.updateOptionalReason(
                                    value,
                                  ),
                                ),
                                Divider(
                                  color: ColorOutlet.systemBorderDisabled,
                                ),
                              ],
                              Text.rich(
                                TextSpan(
                                  children: [
                                    TextSpan(
                                      text: 'Caso realmente queira excluir sua conta, digite ',
                                      style: TextStyle(
                                        color: ColorOutlet.contentSecondary,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: TextPattern().fontFamily,
                                      ),
                                    ),
                                    TextSpan(
                                      text: '"EXCLUIR" ',
                                      style: TextStyle(
                                        color: ColorOutlet.contentSecondary,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        fontFamily: TextPattern().fontFamily,
                                      ),
                                    ),
                                    TextSpan(
                                      text: 'no campo abaixo e confirme para prosseguir.',
                                      style: TextStyle(
                                        color: ColorOutlet.contentSecondary,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w400,
                                        fontFamily: TextPattern().fontFamily,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              FieldPattern(
                                controller: controller.confirmController,
                                hintText: 'EXCLUIR',
                                onChanged: (value) => controller.updateConfirmText(value),
                              ),
                              WarningMessageBox(
                                title: 'Importante',
                                message: 'Esta ação é irreversível e todos os seus dados serão removidos do Promobell permanentemente.',
                                color: ColorOutlet.feedbackWarning,
                                icon: SvgIcons.feedbackWarning,
                              ),
                              SizedBox(
                                height: 44,
                                width: double.maxFinite,
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: controller.isDeleteEnabled
                                        ? ColorOutlet.contentPrimary
                                        : ColorOutlet.contentPrimary.withValues(
                                            alpha: 0.25,
                                          ),
                                    overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
                                  ),
                                  onPressed: controller.isLoadingDeleteAccount
                                      ? null
                                      : () async {
                                          if (controller.isDeleteEnabled) {
                                            String email = Supabase.instance.client.auth.currentUser?.email ?? '';
                                            bool res = await controller.deleteAccount();

                                            if (res) {
                                              // Limpar produtos salvos antes da navegação
                                              await offersController.removedAllSavedProducts(
                                                email,
                                              );
                                              profileController.clearUserData();

                                              // Mostrar mensagem de sucesso
                                              if (context.mounted) {
                                                CustomSnackBar.show(
                                                  context: context,
                                                  message: "Sua conta foi excluída. Você será direcionado para a página de login.",
                                                  icon: SvgIcons.feedbackCheck,
                                                );
                                              }
                                              // A navegação é feita automaticamente pelo método deleteAccount()
                                            } else {
                                              if (context.mounted) {
                                                CustomSnackBar.show(
                                                  context: context,
                                                  message: "Ocorreu um erro ao excluir sua conta. Tente novamente mais tarde.",
                                                  icon: SvgIcons.feedbackWarning,
                                                );
                                              }
                                            }
                                          }
                                        },
                                  child: controller.isLoadingDeleteAccount
                                      ? SizedBox(
                                          height: 24,
                                          width: 24,
                                          child: CircularProgressIndicator(),
                                        )
                                      : TextPattern.customText(
                                          text: 'Excluir conta',
                                          fontSize: 14,
                                          color: ColorOutlet.contentTertiary,
                                        ),
                                ),
                              ),
                              const SizedBox(height: 1),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
