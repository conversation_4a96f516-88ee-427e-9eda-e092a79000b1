import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/controllers/login_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_age.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_init.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_name.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_gender.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final controller = Modular.get<ProfileController>();
  final loginController = Modular.get<LoginController>();
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
  }

  void _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// Fecha o OpenContainer (anima reverso) e, se veio do modal, fecha o modal também.
  Future<void> _closeOnboarding({bool goHomeAfter = false}) async {
    // capture o Navigator uma única vez (continua válido após o pop)
    final nav = Navigator.of(context);

    // 1) fecha a rota aberta pelo OpenContainer (anima reverso)
    if (nav.canPop()) {
      await nav.maybePop(); // aguarda a animação terminar
    } else {
      // Se não há histórico, volta para home diretamente
      Modular.to.pushNamedAndRemoveUntil(
        '/home',
        (route) => false,
        arguments: {'initialIndex': 0, 'maintainIndex': false},
      );
      return; // Sai da função aqui
    }

    // 2) se esse onboarding veio do modal, feche o bottom sheet
    // Usa o controller para verificar se veio do modal (mais confiável)
    if (loginController.onboardingOpenedFromModal && nav.canPop()) {
      await nav.maybePop(); // fecha o ModalBottomSheetRoute
    }

    // 3) opcional: navegar para home depois de tudo fechado
    if (goHomeAfter) {
      Modular.to.pushNamedAndRemoveUntil(
        '/home',
        (route) => false,
        arguments: {'initialIndex': 0, 'maintainIndex': false},
      );
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Scaffold(
            body: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                PageInit(
                  controller: controller,
                  onNext: _nextPage,
                  onClose: () => _closeOnboarding(),
                ),
                PageName(
                  controller: controller,
                  onNext: _nextPage,
                  onClose: () => _closeOnboarding(goHomeAfter: true),
                ),
                PageAge(
                  controller: controller,
                  onBack: _previousPage,
                  onNext: _nextPage,
                  onClose: () => _closeOnboarding(goHomeAfter: true),
                ),
                PageGender(
                  controller: controller,
                  onBack: _previousPage,
                  onClose: () => _closeOnboarding(goHomeAfter: true),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
