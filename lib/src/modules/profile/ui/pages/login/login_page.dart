import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/circular_text_spinner.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/login_button.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/terms_and_privacy_text.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/text_pattern.dart';
import '../../../controllers/login_controller.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final loginController = Modular.get<LoginController>();

  @override
  void initState() {
    loginController.initAuthListener();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final screenH = MediaQuery.of(context).size.height;
    final screenW = MediaQuery.of(context).size.width;
    final double d = (math.max(screenW, screenH) * 0.54).clamp(320.0, 520.0);

    const topOverflow = 0.45;
    const rightOverflow = 0.42;

    return AnimatedBuilder(
      animation: loginController,
      builder: (context, _) {
        return Scaffold(
          backgroundColor: ColorOutlet.contentTertiary,
          body: Stack(
            clipBehavior: Clip.none,
            children: [
              Positioned(
                top: -d * topOverflow,
                right: -d * rightOverflow,
                child: IgnorePointer(
                  child: CircularTextSpinner(
                    text: 'ECONOMIZE PROMOBELL ECONOMIZE PROMOBELL ECONOMIZE PROMOBELL ',
                    diameter: d,
                    period: const Duration(seconds: 30),
                  ),
                ),
              ),

              SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(minHeight: screenH),
                  child: IntrinsicHeight(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Spacer(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(left: 32),
                                  child: SvgPicture.asset(SvgIcons.icon),
                                ),
                                const SizedBox(width: 32),
                              ],
                            ),
                            const SizedBox(height: 24),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 32),
                              child: Column(
                                children: [
                                  TextPattern.customText(
                                    text: 'Entre e economize de verdade',
                                    fontSize: 40,
                                    fontWeightOption: FontWeightOption.bold,
                                    color: ColorOutlet.contentPrimary,
                                  ),
                                  const SizedBox(height: 16),
                                  TextPattern.customText(
                                    text: 'Encontre uma infinidade de produtos em oferta, cupons de desconto e frete grátis. O Promobell te alerta antes de todo mundo.',
                                    fontSize: 14,
                                    color: ColorOutlet.contentSecondary,
                                  ),
                                  const SizedBox(height: 32),
                                  Visibility(
                                    visible: !Platform.isIOS,
                                    replacement: LoginButton(
                                      icon: SvgIcons.brandsApple,
                                      text: 'Continuar com Apple',
                                      onPressed: loginController.isLoadingApple
                                          ? null
                                          : () async {
                                              await loginController.handleAppleSignIn(context);
                                              if (!context.mounted) return;

                                              // Verifica se login foi bem-sucedido
                                              if (Supabase.instance.client.auth.currentUser != null) {
                                                final isComplete = await loginController.isProfileComplete();
                                                if (isComplete) {
                                                  // Perfil completo - vai para home
                                                  Modular.to.pushReplacementNamed('/home');
                                                } else {
                                                  // Perfil incompleto - vai para onboarding
                                                  Modular.to.pushReplacementNamed('/profile/onboarding');
                                                }
                                              }
                                            },
                                      isLoading: loginController.isLoadingApple,
                                    ),
                                    child: LoginButton(
                                      icon: SvgIcons.brandsGoogle,
                                      text: 'Continuar com Google',
                                      onPressed: loginController.isLoading
                                          ? null
                                          : () async {
                                              await loginController.handleGoogleSignIn(context);
                                              if (!context.mounted) return;

                                              // Verifica se login foi bem-sucedido
                                              if (Supabase.instance.client.auth.currentUser != null) {
                                                final isComplete = await loginController.isProfileComplete();
                                                if (isComplete) {
                                                  // Perfil completo - vai para home
                                                  Modular.to.pushReplacementNamed('/home');
                                                } else {
                                                  // Perfil incompleto - vai para onboarding
                                                  Modular.to.pushReplacementNamed('/profile/onboarding');
                                                }
                                              }
                                            },
                                      isLoading: loginController.isLoading,
                                    ),
                                  ),
                                  const SizedBox(height: 24),
                                  SizedBox(
                                    height: 56,
                                    width: double.infinity,
                                    child: ElevatedButton(
                                      onPressed: () async {
                                        await loginController.markFirstAccessComplete();
                                        await loginController.upsertOrCleanupAnonToken();
                                        if (!context.mounted) return;
                                        Modular.to.pushReplacementNamed('/home');
                                      },
                                      child: TextPattern.customText(
                                        text: 'Continuar sem conta',
                                        color: ColorOutlet.contentTertiary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SafeArea(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 32, right: 32, bottom: 12),
                            child: TermsAndPrivacyText(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
