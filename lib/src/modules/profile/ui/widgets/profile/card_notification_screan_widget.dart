import 'package:flutter/material.dart';

import '../../../../../../theme/color_outlet.dart';
import '../../../../../components/text_pattern.dart';

class CardNotificationScreanWidget extends StatelessWidget {
  final String title;
  final String content;
  final Offset offset;
  const CardNotificationScreanWidget({
    super.key,
    required this.title,
    required this.content,
    required this.offset,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 70,
      width: 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.paper,
        border: Border.all(color: ColorOutlet.surface4, width: 1),
        boxShadow: [
          BoxShadow(
            color: ColorOutlet.feedbackSystem.withValues(alpha: 0.4),
            offset: offset,
            blurRadius: 30, // Aumenta o espalhamento da sombra
            spreadRadius: 10, // Faz a sombra crescer além do container
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
        child: Row(
          children: [
            SizedBox(
              height: 50,
              width: 50,
              child: Image.asset(
                'assets/images/iconLoginPage.png',
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: TextPattern.customText(
                          text: title,
                          fontSize: 11,
                          color: ColorOutlet.contentSecondary,
                          fontWeightOption: FontWeightOption.bold,
                        ),
                      ),
                      TextPattern.customText(
                        text: '9:41 AM',
                        fontSize: 9,
                        color: ColorOutlet.contentGhost,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ],
                  ),
                  TextPattern.customText(
                    text: content,
                    fontSize: 10,
                    maxLines: 2,
                    color: ColorOutlet.contentSecondary,
                    fontWeightOption: FontWeightOption.regular,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
