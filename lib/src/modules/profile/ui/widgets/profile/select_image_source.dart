import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import '../../../../../components/drag_indicator.dart';
import '../../../../../components/text_pattern.dart';
import '../../../controllers/profile_controller.dart';

class SelectImageSource extends StatelessWidget {
  const SelectImageSource({super.key, required this.controller});

  final ProfileController controller;

  @override
  Widget build(BuildContext context) {
    const SizedBox spacer = SizedBox(height: 16);
    final double bottomPadding = MediaQuery.of(context).padding.bottom;

    return Container(
      height: 230 + bottomPadding,
      decoration: BoxDecoration(
        color: ColorOutlet.systemSurface,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DragIndicator(),
          Padding(
            padding: EdgeInsets.only(left: 24),
            child: SizedBox(
              height: 32,
              child: TextPattern.customText(
                text: 'Alterar foto',
                fontSize: 20,
                fontWeightOption: FontWeightOption.bold,
              ),
            ),
          ),
          spacer,
          // CustomListTitle(
          //   title: 'Tirar foto',
          //   icon: SvgIcons.archiveCamera,
          //   onTap: () {
          //     controller.pickImage(ImageSource.camera);
          //     Navigator.pop(context);
          //   },
          // ),
          // spacer,
          CustomListTitle(
            title: 'Escolher foto',
            icon: SvgIcons.archiveImageUp,
            onTap: () {
              controller.pickImage(ImageSource.gallery);
              Navigator.pop(context);
            },
          ),
          spacer,
          CustomListTitle(
            title: 'Excluir foto',
            icon: SvgIcons.actionTrash,
            onTap: () async {
              if (context.mounted) {
                Navigator.pop(context);
              }
              await controller.deleteUserImage();
            },
          ),
        ],
      ),
    );
  }
}

class CustomListTitle extends StatelessWidget {
  const CustomListTitle({
    super.key,
    required this.onTap,
    required this.icon,
    required this.title,
  });

  final String icon;
  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: SizedBox(
        height: 48,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            splashColor: ColorOutlet.systemBorderDisabled.withValues(
              alpha: 0.3,
            ),
            highlightColor: ColorOutlet.systemBorderDisabled.withValues(
              alpha: 0.3,
            ),
            borderRadius: BorderRadius.circular(16),
            onTap: onTap,
            child: Container(
              height: 48,
              width: double.infinity,
              padding: const EdgeInsets.only(left: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(icon, width: 24, height: 24),
                  SizedBox(width: 8),
                  TextPattern.customText(text: title, fontSize: 14),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
