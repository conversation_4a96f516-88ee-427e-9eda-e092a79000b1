import 'dart:ui';

import 'package:flutter/material.dart';

import '../../../../../../../theme/color_outlet.dart';

import '../../../../../components/text_pattern.dart';

class CardNotificationBluer extends StatelessWidget {
  const CardNotificationBluer({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ImageFiltered(
          imageFilter: ImageFilter.blur(
            sigmaX: 7,
            sigmaY: 7,
          ), // Desfoque aplicado no card inteiro
          child: _buildCardContainer(),
        ),
      ],
    );
  }

  Widget _buildCardContainer() {
    return Container(
      height: 160,
      width: 350,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.paper,
        border: Border.all(color: ColorOutlet.surface4, width: 1),
        boxShadow: [
          BoxShadow(
            color: ColorOutlet.backgroundContainer.withValues(alpha: 0.3),
            blurRadius: 10,
            spreadRadius: 3,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: _buildCardContent(),
    );
  }

  // Conteúdo do card
  Widget _buildCardContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
      child: Row(
        children: [
          SizedBox(
            height: 80,
            width: 80,
            child: Image.asset(
              'assets/images/iconLoginPage.png',
              fit: BoxFit.cover,
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TextPattern.customText(
                        text: '',
                        fontSize: 16,
                        color: ColorOutlet.contentSecondary,
                        fontWeightOption: FontWeightOption.bold,
                      ),
                    ),
                    TextPattern.customText(
                      text: '9:41 AM',
                      fontSize: 12,
                      color: ColorOutlet.contentGhost,
                      fontWeightOption: FontWeightOption.bold,
                    ),
                  ],
                ),
                TextPattern.customText(
                  text: '',
                  fontSize: 14,
                  maxLines: 2,
                  color: ColorOutlet.contentSecondary,
                  fontWeightOption: FontWeightOption.regular,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
