import 'dart:io';
import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/modules/profile/ui/pages/login/onboarding_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/modules/profile/controllers/login_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/login_button.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/terms_and_privacy_text.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:promobell/theme/svg_icons.dart';

class ExpandableProfileCardLogin extends StatefulWidget {
  const ExpandableProfileCardLogin({super.key});

  @override
  State<ExpandableProfileCardLogin> createState() => _ExpandableProfileCardLoginState();
}

class _ExpandableProfileCardLoginState extends State<ExpandableProfileCardLogin> {
  final loginController = Modular.get<LoginController>();

  /// Guardamos o callback que o OpenContainer fornece no closedBuilder
  VoidCallback? _openContainer;

  Future<void> _afterGoogle() async {
    // Define callback que será executado após login (mesmo se widget for desmontado)
    loginController.setPostLoginCallback(() async {
      if (Supabase.instance.client.auth.currentUser != null) {
        // Verifica se o perfil está completo antes de abrir o OpenContainer
        final isComplete = await loginController.isProfileComplete();

        if (!isComplete) {
          // Define que NÃO veio do modal APÓS login e abre OpenContainer
          loginController.setOnboardingFromModal(false);

          // Verifica se widget ainda está montado antes de abrir OpenContainer
          if (mounted && _openContainer != null) {
            _openContainer!.call(); // abre a animação do container
          } else if (!mounted) {
            // Se widget foi desmontado, navega diretamente com pushNamed para manter histórico
            Modular.to.pushNamed('/profile/onboarding');
          }
        }
      }
    });

    await loginController.handleGoogleSignIn(context, fromModal: false);
  }

  Future<void> _afterApple() async {
    // Define callback que será executado após login (mesmo se widget for desmontado)
    loginController.setPostLoginCallback(() async {
      if (Supabase.instance.client.auth.currentUser != null) {
        // Verifica se o perfil está completo antes de abrir o OpenContainer
        final isComplete = await loginController.isProfileComplete();

        if (!isComplete) {
          // Define que NÃO veio do modal APÓS login e abre OpenContainer
          loginController.setOnboardingFromModal(false);

          // Verifica se widget ainda está montado antes de abrir OpenContainer
          if (mounted && _openContainer != null) {
            _openContainer!.call();
          } else if (!mounted) {
            // Se widget foi desmontado, navega diretamente com pushNamed para manter histórico
            Modular.to.pushNamed('/profile/onboarding');
          }
        }
      }
    });

    await loginController.handleAppleSignIn(context, fromModal: false);
  }

  @override
  Widget build(BuildContext context) {
    final double top = MediaQuery.of(context).padding.top;

    return Stack(
      alignment: Alignment.topCenter,
      clipBehavior: Clip.none,
      children: [
        // ======== CONTAINER TRANSFORM ========
        Padding(
          padding: EdgeInsets.only(left: 16, right: 16, top: top + 44),
          child: OpenContainer<void>(
            tappable: false, // só abre via _openContainer?.call()
            openElevation: 0,
            closedElevation: 0,
            transitionDuration: const Duration(milliseconds: 620),
            transitionType: ContainerTransitionType.fade, // pode trocar por .fadeThrough se preferir
            closedColor: ColorOutlet.paper,
            openColor: ColorOutlet.paper,
            middleColor: ColorOutlet.paper,
            closedShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
            openShape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),

            // PÁGINA DESTINO (fullscreen) – sua OnboardingPage
            openBuilder: (_, __) => const OnboardingPage(),

            // CARD FECHADO – seu card original (capturamos o openContainer)
            closedBuilder: (_, openContainer) {
              _openContainer = openContainer;
              return ClipRRect(
                borderRadius: BorderRadius.circular(24),
                child: Container(
                  decoration: BoxDecoration(
                    color: ColorOutlet.paper,
                    borderRadius: BorderRadius.circular(24),
                  ),
                  padding: const EdgeInsets.only(left: 24, right: 24, bottom: 24, top: 56),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextPattern.customText(
                        text: 'Seja um Promolover',
                        fontSize: 40,
                        fontWeightOption: FontWeightOption.bold,
                        color: ColorOutlet.contentPrimary,
                      ),
                      const SizedBox(height: 16),
                      TextPattern.customText(
                        text:
                            'Com a conta Promolover, os alertas são feitos pra você. '
                            'Economize antes de todo mundo em uma infinidade de produtos em oferta, '
                            'cupons de desconto e frete grátis.',
                        fontSize: 14,
                        color: ColorOutlet.contentSecondary,
                      ),
                      const SizedBox(height: 32),

                      // ===== Botões de login =====
                      Visibility(
                        visible: !Platform.isIOS,
                        replacement: LoginButton(
                          icon: SvgIcons.brandsApple,
                          text: 'Continuar com Apple',
                          onPressed: loginController.isLoadingApple ? null : _afterApple,
                          isLoading: loginController.isLoadingApple,
                        ),
                        child: LoginButton(
                          icon: SvgIcons.brandsGoogle,
                          text: 'Continuar com Google',
                          onPressed: loginController.isLoading ? null : _afterGoogle,
                          isLoading: loginController.isLoading,
                        ),
                      ),
                      const SizedBox(height: 24),
                      const TermsAndPrivacyText(),
                    ],
                  ),
                ),
              );
            },
          ),
        ),

        // ÍCONE de topo (estático, como no design)
        Padding(
          padding: EdgeInsets.only(top: top + 4),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              border: Border.all(color: ColorOutlet.paper, width: 4),
            ),
            child: SizedBox(
              height: 80,
              width: 80,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: ColoredBox(
                  color: ColorOutlet.paper,
                  child: SvgPicture.asset(
                    SvgIcons.iconSmall,
                    fit: BoxFit.cover,
                    height: 80,
                    width: 80,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
