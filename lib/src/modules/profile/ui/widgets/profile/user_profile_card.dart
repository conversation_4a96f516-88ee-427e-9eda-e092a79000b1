import '../../../../../components/text_pattern.dart';
import '../../../../../../theme/color_outlet.dart';
import '../../../../../../theme/svg_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class UserProfileCard extends StatelessWidget {
  final String name;
  final String joinedDate;
  final VoidCallback onPressed;
  final bool isCompletedProfile;

  const UserProfileCard({
    required this.isCompletedProfile,
    required this.onPressed,
    required this.name,
    required this.joinedDate,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isCompletedProfile,
      replacement: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextPattern.customText(
            text: 'Olá, \n${name.trim().isNotEmpty ? name : 'Promolover'}!',
            fontSize: 40,
            fontWeightOption: FontWeightOption.bold,
            textAlign: TextAlign.start,
            color: ColorOutlet.contentPrimary,
          ),
          const SizedBox(height: 16),
          TextPattern.customText(
            text: 'Sua conta foi criada, e nada nos deixa mais feliz!',
            fontSize: 24,
            fontWeightOption: FontWeightOption.bold,
            textAlign: TextAlign.start,
          ),
          const SizedBox(height: 16),
          TextPattern.customText(
            text: 'Complete seu perfil rapidinho pra deixar tudo com a sua cara enquanto usa o app.',
            fontSize: 16,
            fontWeightOption: FontWeightOption.regular,
            textAlign: TextAlign.start,
            color: ColorOutlet.contentSecondary,
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: onPressed,
              child: TextPattern.customText(
                text: 'Completar perfil',
                color: ColorOutlet.contentTertiary,
              ),
            ),
          ),
        ],
      ),
      child: Column(
        spacing: 16,
        children: [
          SizedBox(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(width: 16),
                TextPattern.customText(
                  text: name,
                  fontSize: 20,
                  fontWeightOption: FontWeightOption.bold,
                ),
                SizedBox(width: 4),
                SvgPicture.asset(
                  SvgIcons.markerVerifiedFilled,
                  colorFilter: ColorFilter.mode(
                    ColorOutlet.contentPrimary,
                    BlendMode.srcIn,
                  ),
                  height: 20,
                  width: 20,
                ),
              ],
            ),
          ),
          TextPattern.customText(
            text: 'Economizando desde $joinedDate',
            fontSize: 12,
            color: ColorOutlet.contentGhost,
          ),
          TextButton(
            onPressed: onPressed,
            child: TextPattern.customText(
              text: 'Editar conta',
              color: ColorOutlet.contentPrimary,
            ),
          ),
        ],
      ),
    );
  }
}
