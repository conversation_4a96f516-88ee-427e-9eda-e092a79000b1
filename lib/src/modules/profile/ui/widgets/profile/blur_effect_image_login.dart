import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:promobell/theme/color_outlet.dart';

class BlurEffectImageLogin extends StatelessWidget {
  const BlurEffectImageLogin({super.key});

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: <PERSON>gn(
        alignment: Alignment.center,
        heightFactor: 0.5,
        child: ImageFiltered(
          imageFilter: ImageFilter.blur(sigmaX: 24, sigmaY: 24),
          child: Container(
            height: 510,
            color: ColorOutlet.feedbackDisabled,
          ),
        ),
      ),
    );
  }
}
