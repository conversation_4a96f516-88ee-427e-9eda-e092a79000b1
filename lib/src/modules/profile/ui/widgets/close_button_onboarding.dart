import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:promobell/theme/svg_icons.dart';

class CloseButtonOnboarding extends StatelessWidget {
  final VoidCallback? onPressed;

  const CloseButtonOnboarding({super.key, this.onPressed});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: 32 + MediaQuery.of(context).padding.top,
        bottom: 24,
      ),
      child: GestureDetector(
        onTap: onPressed,
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: ColorOutlet.paper,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: SvgPicture.asset(
              SvgIcons.actionClose,
              colorFilter: ColorFilter.mode(
                ColorOutlet.contentSecondary,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
