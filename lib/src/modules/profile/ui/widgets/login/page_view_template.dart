import 'package:flutter/material.dart';
import 'package:promobell/theme/color_outlet.dart';

class PageViewTemplate extends StatelessWidget {
  final Widget? child;
  const PageViewTemplate({this.child, super.key});

  @override
  Widget build(BuildContext context) {
    final sizeHeight = MediaQuery.of(context).size.height;
    return SingleChildScrollView(
      child: Container(
        color: ColorOutlet.paper,
        height: sizeHeight,
        padding: const EdgeInsets.only(left: 24, right: 24, bottom: 24),
        child: child,
      ),
    );
  }
}
