import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:promobell/theme/color_outlet.dart';

import '../../../../../../theme/line_height.dart';

class CustomTextInput extends StatelessWidget {
  final String text;
  final double? width;
  final String? Function(String?)? validator;
  final bool isCenter;
  final TextEditingController? controller;
  final FocusNode? focusNode;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputAction? textInputAction;
  final void Function(String)? onFieldSubmitted;
  final void Function(String)? onChanged;

  const CustomTextInput({
    this.isCenter = false,
    this.focusNode,
    this.width,
    required this.controller,
    required this.text,
    required this.validator,
    this.inputFormatters,
    this.textInputAction,
    this.onFieldSubmitted,
    this.onChanged,
    this.keyboardType = TextInputType.text,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        textAlign: isCenter ? TextAlign.center : TextAlign.start,
        onChanged: onChanged,
        keyboardType: keyboardType,
        inputFormatters: inputFormatters,
        textInputAction: textInputAction,
        onFieldSubmitted: onFieldSubmitted,
        style: TextStyle(
          color: ColorOutlet.contentPrimary,
          fontSize: 16,
          height: LineHeight.lh24f16,
          fontFamily: 'Figtree',
        ),
        decoration: InputDecoration(
          hintText: focusNode?.hasFocus == true ? null : text,
          hintStyle: TextStyle(
            color: ColorOutlet.contentGhost,
            height: LineHeight.lh24f16,
            fontSize: 16,
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 14,
            horizontal: 16,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide(
              color: controller!.text.isNotEmpty
                  ? ColorOutlet.contentSecondary
                  : ColorOutlet.contentGhost,
              width: 1,
            ),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: ColorOutlet.contentSecondary,
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: const BorderSide(
              color: ColorOutlet.contentPrimary,
              width: 1,
            ),
          ),
        ),

        validator: validator,
      ),
    );
  }
}
