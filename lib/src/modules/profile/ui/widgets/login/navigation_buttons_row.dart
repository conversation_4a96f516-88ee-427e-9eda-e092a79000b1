import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';

class NavigationButtonsRow extends StatelessWidget {
  final bool onlyButton;
  final void Function()? onNext;
  final void Function()? onBack;
  final String text;

  const NavigationButtonsRow({
    this.onNext,
    this.onBack,
    this.onlyButton = false,
    super.key,
    this.text = 'Continuar',
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Visibility(
          visible: !onlyButton,
          child: TextButton(
            onPressed: onBack,
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 29),
            ),
            child: TextPattern.customText(
              text: 'Voltar',
              color: ColorOutlet.contentSecondary,
            ),
          ),
        ),
        Visibility(visible: !onlyButton, child: Sized<PERSON><PERSON>(width: 16)),
        Expanded(
          child: <PERSON><PERSON><PERSON><PERSON>(
            height: 44,
            child: ElevatedButton(
              onPressed: onNext,
              child: TextPattern.customText(
                text: text,
                color: ColorOutlet.contentTertiary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
