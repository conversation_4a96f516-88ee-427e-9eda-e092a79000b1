import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';

class LoginButton extends StatelessWidget {
  final String icon;
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  const LoginButton({
    required this.icon,
    required this.text,
    required this.onPressed,
    required this.isLoading,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: ColorOutlet.paper,
        foregroundColor: ColorOutlet.contentSecondary,
        minimumSize: const Size(double.infinity, 56),
        overlayColor: ColorOutlet.systemBorderDisabled.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: ColorOutlet.contentDisabled, width: 1),
        ),
      ),
      onPressed: onPressed,
      child: isLoading
          ? SizedBox(
              height: 24,
              width: 24,
              child: CircularProgressIndicator(strokeWidth: 1.5),
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(icon, height: 24),
                const SizedBox(width: 12),
                TextPattern.customText(
                  text: text,
                  fontSize: 14,
                  color: ColorOutlet.contentSecondary,
                  fontWeightOption: FontWeightOption.medium,
                ),
              ],
            ),
    );
  }
}
