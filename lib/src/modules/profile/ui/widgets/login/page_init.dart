import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/modules/profile/controllers/profile_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/circular_text_spinner.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/navigation_buttons_row.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/page_view_template.dart';
import '../../../../../../theme/color_outlet.dart';

class PageInit extends StatefulWidget {
  final VoidCallback onNext;
  final ProfileController controller;

  final Future<void> Function()? onClose;

  const PageInit({
    required this.controller,
    required this.onNext,
    this.onClose,
    super.key,
  });

  @override
  State<PageInit> createState() => _PageInitState();
}

class _PageInitState extends State<PageInit> {
  late TextEditingController nameController;
  late TextEditingController surnameController;
  final nameFocusController = FocusNode();
  final surnameFocusController = FocusNode();
  final formKey = GlobalKey<FormState>();

  void _updateControllerValues() {
    widget.controller.setUserName(
      nameController.text,
      surnameController.text,
    );
  }

  @override
  void initState() {
    nameController = TextEditingController(text: widget.controller.userName)..addListener(_updateControllerValues);
    surnameController = TextEditingController(text: widget.controller.userSurname)..addListener(_updateControllerValues);
    super.initState();
  }

  @override
  void dispose() {
    nameController.dispose();
    surnameController.dispose();
    nameFocusController.dispose();
    surnameFocusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenH = MediaQuery.of(context).size.height;
    final screenW = MediaQuery.of(context).size.width;
    final double d = (math.max(screenW, screenH) * 0.54).clamp(320.0, 520.0);

    const topOverflow = 0.45;
    const rightOverflow = 0.42;

    final paddingBottom = MediaQuery.of(context).padding.bottom + 16;

    return AnimatedBuilder(
      animation: Listenable.merge([
        nameController,
        surnameController,
        nameFocusController,
        surnameFocusController,
      ]),
      builder: (context, _) {
        return PageViewTemplate(
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Positioned(
                top: -d * topOverflow,
                right: -d * rightOverflow,
                child: IgnorePointer(
                  child: CircularTextSpinner(
                    text: 'ECONOMIZE PROMOBELL ECONOMIZE PROMOBELL ECONOMIZE PROMOBELL ',
                    diameter: d,
                    period: const Duration(seconds: 30),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Spacer(),
                  TextPattern.customText(
                    text: 'Olá,\n${(widget.controller.userName?.trim().isNotEmpty ?? false) ? widget.controller.userName : 'Promolover'}!',
                    fontSize: 40,
                    fontWeightOption: FontWeightOption.bold,
                    textAlign: TextAlign.start,
                    color: ColorOutlet.contentPrimary,
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.035),
                  TextPattern.customText(
                    text: 'Sua conta foi criada, e nada nos deixa mais feliz!',
                    fontSize: 24,
                    fontWeightOption: FontWeightOption.bold,
                    textAlign: TextAlign.start,
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.035),
                  TextPattern.customText(
                    text: 'Complete seu perfil rapidinho pra deixar tudo com a sua cara enquanto usa o app.',
                    fontSize: 16,
                    fontWeightOption: FontWeightOption.regular,
                    textAlign: TextAlign.start,
                    color: ColorOutlet.contentGhost,
                  ),
                  SizedBox(height: MediaQuery.of(context).size.height * 0.10),
                  NavigationButtonsRow(
                    text: 'Completar perfil',
                    onlyButton: true,
                    onBack: () {},
                    onNext: widget.onNext,
                  ),
                  const SizedBox(height: 16),
                  TextButton(
                    onPressed: () async {
                      // fecha com segurança (sem navegação concorrente)
                      if (widget.onClose != null) {
                        await widget.onClose!.call();
                      } else {
                        // fallback: fecha a rota atual
                        final nav = Navigator.of(context);
                        if (nav.canPop()) await nav.maybePop();
                      }
                    },
                    child: Center(
                      child: TextPattern.customText(
                        text: 'Depois',
                        color: ColorOutlet.contentSecondary,
                      ),
                    ),
                  ),
                  SizedBox(height: paddingBottom),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
