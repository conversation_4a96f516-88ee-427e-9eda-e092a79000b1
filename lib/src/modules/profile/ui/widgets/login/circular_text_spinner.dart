import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart' show TextPattern;
import 'package:promobell/theme/color_outlet.dart';

class CircularTextSpinner extends StatefulWidget {
  const CircularTextSpinner({
    super.key,
    required this.text,
    this.diameter = 280,
    this.period = const Duration(seconds: 18),
    this.textStyle,
  });

  final String text;
  final double diameter;
  final Duration period;
  final TextStyle? textStyle;

  @override
  State<CircularTextSpinner> createState() => _CircularTextSpinnerState();
}

class _CircularTextSpinnerState extends State<CircularTextSpinner> with SingleTickerProviderStateMixin {
  late final AnimationController _ctrl = AnimationController(vsync: this)..repeat(period: widget.period);

  @override
  void didUpdateWidget(covariant CircularTextSpinner oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.period != widget.period) {
      _ctrl.duration = widget.period;
      _ctrl.repeat(period: widget.period);
    }
  }

  @override
  void dispose() {
    _ctrl.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _ctrl,
      builder: (_, __) {
        final angle = _ctrl.value * 2 * math.pi;
        return CustomPaint(
          size: Size.square(widget.diameter),
          painter: _CircularTextPainter(
            text: widget.text,
            startAngle: angle,
            style:
                widget.textStyle ??
                TextStyle(
                  fontFamily: TextPattern().fontFamily,
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: ColorOutlet.contentSecondary,
                ),
          ),
        );
      },
    );
  }
}

class _CircularTextPainter extends CustomPainter {
  _CircularTextPainter({
    required this.text,
    required this.startAngle,
    required this.style,
  });

  final String text;
  final double startAngle;
  final TextStyle style;

  @override
  void paint(Canvas canvas, Size size) {
    final radius = size.width / 2;
    final center = Offset(size.width / 2, size.height / 2);
    canvas.translate(center.dx, center.dy);

    final runes = text.runes.toList();
    final step = 2 * math.pi / runes.length;

    // ===== Mapear pesos por palavra =====
    final weights = List<FontWeight>.filled(
      runes.length,
      style.fontWeight ?? FontWeight.w600, // base
      growable: false,
    );

    void applyToken(String token, FontWeight weight) {
      final tokenRunes = token.runes.toList();
      for (int i = 0; i <= runes.length - tokenRunes.length; i++) {
        bool match = true;
        for (int j = 0; j < tokenRunes.length; j++) {
          if (runes[i + j] != tokenRunes[j]) {
            match = false;
            break;
          }
        }
        if (match) {
          for (int k = 0; k < tokenRunes.length; k++) {
            weights[i + k] = weight;
          }
          i += tokenRunes.length - 1;
        }
      }
    }

    applyToken('ECONOMIZE', FontWeight.w600);
    applyToken('PROMOBELL', FontWeight.w900);

    double angle = startAngle;
    for (int idx = 0; idx < runes.length; idx++) {
      final ch = String.fromCharCode(runes[idx]);

      final tp = TextPainter(
        text: TextSpan(
          text: ch,
          style: style.copyWith(fontWeight: weights[idx]),
        ),
        textDirection: TextDirection.ltr,
      )..layout();

      final x = radius * math.cos(angle);
      final y = radius * math.sin(angle);

      canvas.save();
      canvas.translate(x, y);
      canvas.rotate(angle + math.pi / 2); // tangente ao círculo
      tp.paint(canvas, Offset(-tp.width / 2, -tp.height));
      canvas.restore();

      angle += step;
    }
  }

  @override
  bool shouldRepaint(covariant _CircularTextPainter old) => old.text != text || old.startAngle != startAngle || old.style != style;
}
