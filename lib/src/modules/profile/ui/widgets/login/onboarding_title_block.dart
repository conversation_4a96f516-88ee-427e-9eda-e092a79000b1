import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/theme/color_outlet.dart';

class OnboardingTitleBlock extends StatelessWidget {
  final String title;
  final String subtitle;

  const OnboardingTitleBlock({
    required this.title,
    required this.subtitle,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 24,
      children: [
        TextPattern.customText(
          text: title,
          fontWeightOption: FontWeightOption.bold,
          maxLines: 3,
          fontSize: 24,
        ),
        TextPattern.customText(
          text: subtitle,
          fontWeightOption: FontWeightOption.regular,
          color: ColorOutlet.contentGhost,
          fontSize: 16,
        ),
      ],
    );
  }
}
