import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/modules/profile/ui/pages/login/onboarding_page.dart';
import 'package:promobell/src/modules/profile/ui/pages/profile/save_products_page.dart';

import '../../components/custom_transitions.dart';
import 'ui/pages/login/login_page.dart';
import 'ui/pages/profile/about_the_promobell.dart';
import 'ui/pages/profile/delete_account.dart';
import 'ui/pages/profile/privacy_politics.dart';
import 'ui/pages/profile/profile_page.dart';
import 'ui/pages/profile/terms_of_use.dart';

class ProfileModule extends Module {
  @override
  void routes(r) {
    r.child(
      '/',
      child: (context) => ProfilePage(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      '/login',
      child: (context) => const LoginPage(),
      transition: TransitionType.scale,
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      '/about_the_promobell',
      child: (context) => const AboutPromobell(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      '/privacy_politics',
      child: (context) => const PrivacyPolitics(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      '/privacy_use',
      child: (context) => const TermsOfUse(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      '/delete_account',
      child: (context) => DeleteAccount(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
    r.child(
      '/onboarding',
      child: (context) => const OnboardingPage(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/save_products',
      child: (context) => const SaveProductsPage(),
      transition: TransitionType.custom,
      customTransition: CustomTransitions.fromRight(),
      duration: const Duration(milliseconds: 200),
    );
  }
}
