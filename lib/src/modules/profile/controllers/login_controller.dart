import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:promobell/src/modules/offers/controllers/offers_controller.dart';
import 'package:promobell/src/modules/offers/controllers/story/story_controller.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../../../firebase_messaging_service.dart';
import '../../../../supa_base_config_keys.dart';
import '../../../../theme/svg_icons.dart';
import '../../../components/custom_snack_bar.dart';
import '../../../core/base/controllers/base_controller/base_controller.dart';
import '../../../models/user_delete_profile.dart';
import '../../../models/user_profile_model.dart';
import '../../../services/integrity/app_integrity_service.dart';
import '../../../services/logs/app_logger.dart';
import '../../../services/supabase/delete_account/delete_account_db.dart';
import '../../../services/supabase/usuarios/get/get_usuarios.dart';
import '../../../services/supabase/usuarios/post/post_usuarios.dart';
import 'profile_controller.dart';

class LoginController with ChangeNotifier {
  final PostUsuarios _postUsuarios;
  final GetUsuarios _getUsuarios;
  final SupaBaseConfigKeys _supaBaseConfigKeys;
  final AppIntegrityService _integrityService = AppIntegrityService();

  LoginController({
    required PostUsuarios postUsuarios,
    required GetUsuarios getUsuarios,
    required SupaBaseConfigKeys supaBaseConfigKeys,
  }) : _postUsuarios = postUsuarios,
       _getUsuarios = getUsuarios,
       _supaBaseConfigKeys = supaBaseConfigKeys;

  UserProfileModel? userProfile = UserProfileModel.empty();
  final supabase = Supabase.instance.client;
  late StreamSubscription<AuthState> _authSubscription;
  Future<tz.TZDateTime> getBrazilDateTime() async {
    final saoPauloTimeZone = tz.getLocation('America/Sao_Paulo');
    final now = tz.TZDateTime.now(saoPauloTimeZone);
    return now;
  }

  final StoryController storyController = Modular.get<StoryController>();
  final OffersController offersController = Modular.get<OffersController>();

  final FirebaseMessagingService firebaseMessagingService = FirebaseMessagingService();

  // Controla se o onboarding atual foi aberto via modal
  bool _onboardingOpenedFromModal = false;
  bool get onboardingOpenedFromModal => _onboardingOpenedFromModal;

  void setOnboardingFromModal(bool fromModal) {
    _onboardingOpenedFromModal = fromModal;
    notifyListeners();
  }

  // Callback para ser executado após login bem-sucedido (para widgets que podem ser desmontados)
  Future<void> Function()? _postLoginCallback;

  void setPostLoginCallback(Future<void> Function()? callback) {
    _postLoginCallback = callback;
  }

  void initAuthListener() {
    _authSubscription = supabase.auth.onAuthStateChange.listen((
      data,
    ) async {
      final event = data.event;
      if (event == AuthChangeEvent.signedIn) {
        final user = data.session?.user;
        final provider = user?.appMetadata['provider'] as String?;

        final prefs = await SharedPreferences.getInstance();
        final appleFullName = prefs.getString('appleFullName');

        String token = await firebaseMessagingService.getToken() ?? '';

        final nameFromApple = (provider == 'apple' && appleFullName != null && appleFullName.isNotEmpty) ? appleFullName : user?.userMetadata?['full_name'] ?? '';

        userProfile = UserProfileModel(
          name: nameFromApple.trim(),
          email: user?.email,
          image: user?.userMetadata?['avatar_url'],
          accessToken: data.session?.accessToken,
          joinedDate: DateTime.now(),
          tokenFirebase: token,
        );

        try {
          final existingUser = await _getUsuarios.getUserByEmail(
            userProfile!.email!,
          );

          if (existingUser != null) {
            userProfile = existingUser;
            AppLogger.logInfo('Usuário existente encontrado');
          } else {
            AppLogger.logInfo('Criando novo usuário');
            await _postUsuarios.createUser(userProfile!);
            final userData = await _getUsuarios.getUserByEmail(
              userProfile!.email!,
            );
            userProfile = userData;
          }

          await prefs.remove('appleFullName');

          await _handlePostSignIn(provider ?? 'google');
        } catch (e) {
          AppLogger.logError(
            'Erro ao processar usuário',
            e,
            StackTrace.current,
          );
        }
      }
    });
  }

  Future<void> _handlePostSignIn(String provider) async {
    if (userProfile?.accessToken != null && userProfile?.email != null) {
      await saveUserData(userProfile!);
      await saveAccessToken(userProfile!.accessToken!, provider);

      final prefs = await SharedPreferences.getInstance();

      // 🔹 Nome + sobrenome vindos do login
      final fullName = userProfile?.name?.trim() ?? '';
      final nameParts = fullName.split(' ');
      final firstName = nameParts.isNotEmpty ? nameParts.first : '';
      final surname = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

      // 🔹 Envia para o ProfileController
      final profileCtrl = Modular.get<ProfileController>();
      profileCtrl.setUserName(firstName, surname);

      // 🔹 Salva localmente também (SharedPreferences)
      await prefs.setString('userName', firstName);
      await prefs.setString('userSurname', surname);

      // 🔹 Marca primeiro acesso como completo
      await markFirstAccessComplete();

      // 🔹 Navegação agora é controlada pelos widgets específicos (modal/card)
      // Não navegar automaticamente aqui para evitar conflito com OpenContainer
    } else {
      AppLogger.logError(
        'Token de acesso não disponível',
        null,
        StackTrace.current,
      );
    }
  }

  @override
  void dispose() {
    _authSubscription.cancel();
    super.dispose();
  }

  Future<AuthResponse> login() async {
    try {
      final webClientId = _supaBaseConfigKeys.googleWebClientId;
      final androidClientId = _supaBaseConfigKeys.androidClientId;
      final iosClientId = _supaBaseConfigKeys.iosClientId;

      AppLogger.logInfo(
        'Iniciando login Google - WebClientId: ${webClientId.isNotEmpty == true ? "Configurado" : "Não configurado"}',
      );
      AppLogger.logInfo(
        'AndroidClientId: ${androidClientId.isNotEmpty == true ? "Configurado" : "Não configurado"}',
      );
      AppLogger.logInfo(
        'iOSClientId: ${iosClientId.isNotEmpty == true ? "Configurado" : "Não configurado"}',
      );

      final user = supabase.auth.currentUser;
      final GoogleSignIn googleSignIn = GoogleSignIn(
        clientId: user?.userMetadata?['provider'] == 'apple' ? iosClientId : androidClientId,
        serverClientId: webClientId,
      );

      AppLogger.logInfo('Tentando fazer login com Google Sign-In...');
      final googleUser = await googleSignIn.signIn();

      if (googleUser == null) {
        AppLogger.logError(
          'Google Sign-In cancelado pelo usuário',
          null,
          StackTrace.current,
        );
        throw 'Login cancelado pelo usuário';
      }

      AppLogger.logInfo('Usuário Google obtido: ${googleUser.email}');
      final googleAuth = await googleUser.authentication;
      final accessToken = googleAuth.accessToken;
      final idToken = googleAuth.idToken;

      if (accessToken == null) {
        AppLogger.logError(
          'Access Token não encontrado após autenticação Google',
          null,
          StackTrace.current,
        );
        throw 'Nenhum Access Token encontrado.';
      }
      if (idToken == null) {
        AppLogger.logError(
          'ID Token não encontrado após autenticação Google',
          null,
          StackTrace.current,
        );
        throw 'Nenhum ID Token encontrado.';
      }

      AppLogger.logInfo(
        'Tokens obtidos com sucesso - AccessToken: ${accessToken.substring(0, 10)}...',
      );

      userProfile = UserProfileModel(
        name: user?.userMetadata?['full_name'],
        email: user?.email,
        image: user?.userMetadata?['avatar_url'],
        accessToken: user?.userMetadata?['access_token'],
      );

      notifyListeners();

      AppLogger.logInfo(
        'Tentando autenticar com Supabase usando tokens Google...',
      );
      return supabase.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken,
        accessToken: accessToken,
      );
    } catch (e) {
      // Log detalhado do erro
      if (e is PlatformException) {
        AppLogger.logError(
          'Erro PlatformException no Google Sign-In',
          'Code: ${e.code}, Message: ${e.message}, Details: ${e.details}',
          StackTrace.current,
        );

        // Tratamento específico para erros conhecidos
        switch (e.code) {
          case 'sign_in_failed':
            AppLogger.logError(
              'Falha no Google Sign-In - Possíveis causas:',
              '1. SHA-1 não configurado corretamente no Firebase/Google Console\n'
                  '2. Client ID incorreto para o ambiente de produção\n'
                  '3. App não autorizado no Google Console\n'
                  '4. Problema de conectividade\n'
                  'Detalhes: ${e.details}',
              StackTrace.current,
            );
            break;
          case 'network_error':
            AppLogger.logError(
              'Erro de rede no Google Sign-In',
              e.message,
              StackTrace.current,
            );
            break;
          case 'sign_in_canceled':
            AppLogger.logInfo('Login Google cancelado pelo usuário');
            break;
          default:
            AppLogger.logError(
              'Erro desconhecido no Google Sign-In',
              'Code: ${e.code}, Message: ${e.message}',
              StackTrace.current,
            );
        }
      } else {
        AppLogger.logError(
          'Erro geral no login Google',
          e.toString(),
          StackTrace.current,
        );
      }

      debugPrint('Login erro detalhado: $e');
      rethrow;
    }
  }

  bool isLoading = false;
  bool isLoadingSignOut = false;

  /// Verifica a integridade do dispositivo antes do login
  Future<Map<String, dynamic>?> _verifyDeviceIntegrity() async {
    try {
      AppLogger.logInfo(
        'Iniciando verificação de integridade do dispositivo',
      );

      // Verifica se o dispositivo suporta verificação de integridade
      final isSupported = await _integrityService.isIntegritySupported();
      if (!isSupported) {
        AppLogger.logInfo(
          'Dispositivo não suporta verificação de integridade',
        );
        return null;
      }

      // Obtém o token de integridade
      final integrityResult = await _integrityService.getIntegrityToken(
        gcpProjectId: _supaBaseConfigKeys.gcpProjectId,
      );

      if (integrityResult['success'] == true) {
        AppLogger.logInfo(
          'Verificação de integridade concluída com sucesso',
        );
        return integrityResult;
      } else {
        AppLogger.logError(
          'Falha na verificação de integridade',
          integrityResult['error'] ?? 'Erro desconhecido',
          StackTrace.current,
        );
        return null;
      }
    } catch (e) {
      AppLogger.logError(
        'Erro na verificação de integridade',
        e.toString(),
        StackTrace.current,
      );
      return null;
    }
  }

  Future<void> handleGoogleSignIn(BuildContext context, {bool fromModal = false}) async {
    // Define se o onboarding será aberto via modal
    setOnboardingFromModal(fromModal);

    isLoading = true;
    notifyListeners();

    try {
      // 🔹 Verifica a integridade do dispositivo antes do login
      final integrityResult = await _verifyDeviceIntegrity();

      if (integrityResult != null) {
        AppLogger.logInfo(
          'Dispositivo verificado - prosseguindo com login',
        );
      } else {
        AppLogger.logInfo(
          'Verificação de integridade falhou - prosseguindo com login (modo compatibilidade)',
        );
      }

      await login().then((value) {
        if (context.mounted) {
          CustomSnackBar.show(
            context: context,
            message: "Login realizado com sucesso",
            icon: SvgIcons.feedbackCheck,
          );
        }
      });
      await upsertOrCleanupAnonToken();
      await storyController.getStories();

      // Recuperar curtidas e produtos salvos do usuário
      await _recoverUserLikesAndSavedProducts();

      // Se for chamado do modal ou widget, NÃO navegar - deixar o widget decidir
      // A navegação agora é controlada pelos widgets após verificarem isProfileComplete()

      // Executa callback pós-login se definido (para widgets que podem ser desmontados)
      if (_postLoginCallback != null) {
        await _postLoginCallback!.call();
        _postLoginCallback = null; // Limpa após usar
      }
    } catch (e) {
      AppLogger.logError(
        'Erro no handleGoogleSignIn',
        e.toString(),
        StackTrace.current,
      );

      String errorMessage = "Erro ao realizar login";

      // Mensagens específicas baseadas no tipo de erro
      if (e is PlatformException) {
        switch (e.code) {
          case 'sign_in_failed':
            errorMessage = "Falha na autenticação. Verifique sua conexão e tente novamente.";
            break;
          case 'network_error':
            errorMessage = "Erro de conexão. Verifique sua internet e tente novamente.";
            break;
          case 'sign_in_canceled':
            errorMessage = "Login cancelado";
            break;
          default:
            errorMessage = "Erro de autenticação: ${e.message ?? 'Erro desconhecido'}";
        }
      } else if (e.toString().contains('cancelado pelo usuário')) {
        errorMessage = "Login cancelado pelo usuário";
      } else if (e.toString().contains('Access Token')) {
        errorMessage = "Erro na configuração de autenticação. Contate o suporte.";
      } else if (e.toString().contains('ID Token')) {
        errorMessage = "Erro na configuração de autenticação. Contate o suporte.";
      }

      debugPrint('Erro ao realizar login: $e');
      if (context.mounted) {
        CustomSnackBar.show(
          context: context,
          message: errorMessage,
          icon: SvgIcons.feedbackInfo,
        );
      }
    } finally {
      isLoading = false;
      notifyListeners();
    }
  }

  final baseController = Modular.get<BaseController>();
  final profileController = Modular.get<ProfileController>();
  Future<void> logOut() async {
    await signOut();
    // Modular.to.pushReplacementNamed('/profile/login');
    baseController.index = 2;
    notifyListeners();
  }

  Future<void> signOut() async {
    try {
      isLoadingSignOut = true;
      notifyListeners();
      final GoogleSignIn googleSignIn = GoogleSignIn();
      await googleSignIn.signOut();
      await supabase.auth.signOut();
      await clearAccessToken();
      await clearUserData();
      userProfile = UserProfileModel.empty();
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      // limpar story, limpar curtidas e produtos salvos
      storyController.clearStories();
      await offersController.removeAllLikedProductsOnlyApp();
      await offersController.removeAllSavedProductsOnlyApp();
      await offersController.removeAllFollowedCategoriesOnlyApp();

      // Recarregar produtos para mostrar apenas números reais de curtidas
      offersController.fetchNextProdutos();

      notifyListeners();
    } catch (e) {
      debugPrint('Erro ao realizar logout: $e');
      rethrow;
    } finally {
      isLoadingSignOut = false;
      notifyListeners();
    }
  }

  bool isLoadingApple = false;
  Future<void> handleAppleSignIn(BuildContext context, {bool fromModal = false}) async {
    // Define se o onboarding será aberto via modal
    setOnboardingFromModal(fromModal);

    if (!Platform.isIOS && !Platform.isMacOS) {
      if (context.mounted) {
        CustomSnackBar.show(
          context: context,
          message: "Para fazer login com Apple é necessário estar utilizando um dispositivo IOS.",
          icon: SvgIcons.feedbackInfo,
        );
      }
      return;
    }

    isLoadingApple = true;
    notifyListeners();

    try {
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      if (credential.identityToken == null) {
        throw 'Nenhum Identity Token encontrado.';
      }

      // 🔹 Nome completo manualmente resgatado
      final fullName = credential.givenName != null ? '${credential.givenName} ${credential.familyName ?? ''}'.trim() : null;

      // 🔹 Armazenar temporariamente para usar depois
      SharedPreferences prefs = await SharedPreferences.getInstance();
      if (fullName != null && fullName.isNotEmpty) {
        await prefs.setString('appleFullName', fullName);
      }
      prefs.setString('userEmail', credential.email ?? '');

      await supabase.auth.signInWithIdToken(
        provider: OAuthProvider.apple,
        idToken: credential.identityToken!,
      );

      await upsertOrCleanupAnonToken();
      await storyController.getStories();

      // Recuperar curtidas e produtos salvos do usuário
      await _recoverUserLikesAndSavedProducts();

      // Se for chamado do modal ou widget, NÃO navegar - deixar o widget decidir
      // A navegação agora é controlada pelos widgets após verificarem isProfileComplete()

      // Executa callback pós-login se definido (para widgets que podem ser desmontados)
      if (_postLoginCallback != null) {
        await _postLoginCallback!.call();
        _postLoginCallback = null; // Limpa após usar
      }
    } catch (e) {
      debugPrint('Erro ao realizar login: $e');
      if (context.mounted) {
        CustomSnackBar.show(
          context: context,
          message: "Erro ao realizar login com Apple",
          icon: SvgIcons.feedbackInfo,
        );
      }
    } finally {
      isLoadingApple = false;
      notifyListeners();
    }
  }

  Future<void> saveUserData(UserProfileModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('userName', user.name ?? '');
    await prefs.setString('userSurname', user.surname ?? '');
    await prefs.setString('userEmail', user.email ?? '');
    await prefs.setString('userImage', user.image ?? '');
    await prefs.setString('userPhone', user.phone ?? '');
    await prefs.setString(
      'userJoinedDate',
      user.joinedDate?.toIso8601String() ?? '',
    );
    // Só salva gender e birthDate se não forem null
    if (user.gender != null) {
      await prefs.setString('gender', user.gender.toString());
    }
    if (user.birthDate != null) {
      await prefs.setString(
        'birthDate',
        user.birthDate!.toIso8601String(),
      );
    }
  }

  Future<void> clearUserData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('userName');
    await prefs.remove('userSurname');
    await prefs.remove('userEmail');
    await prefs.remove('userImage');
    await prefs.remove('userPhone');
    await prefs.remove('userJoinedDate');
    await prefs.remove('gender');
    await prefs.remove('birthDate');
    await prefs.clear();

    AppLogger.logInfo('Dados do usuário limpos.');
  }

  Future<void> saveAccessToken(
    String accessToken,
    String provider,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('accessToken', accessToken);
    await prefs.setString('authProvider', provider);
    debugPrint('AccessToken salvo para $provider');
  }

  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('accessToken');
  }

  Future<String?> getAuthProvider() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('authProvider');
  }

  Future<void> clearAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('accessToken');
    await prefs.remove('authProvider');
    AppLogger.logInfo('AccessToken e authProvider limpos.');
  }

  // Delete account

  String? selectedReason;
  TextEditingController optionalReasonController = TextEditingController();
  TextEditingController confirmController = TextEditingController();

  /// Atualiza o motivo selecionado
  void updateSelectedReason(String? reason) {
    selectedReason = reason;
    notifyListeners();
  }

  /// Atualiza o texto digitado no campo "EXCLUIR"
  void updateConfirmText(String value) {
    confirmController.text = value;
    notifyListeners();
  }

  // Atualiza o texto digitado no campo "Outro motivo"

  void updateOptionalReason(String value) {
    optionalReasonController.text = value;
    notifyListeners();
  }

  /// Validação para ativar/desativar o botão "Excluir conta"
  bool get isDeleteEnabled {
    final reasonSelected = selectedReason != null;
    // final isOtherReasonValid = selectedReason != 'Outro motivo' || (optionalReasonController.text.trim().isNotEmpty);
    final isConfirmValid = confirmController.text.trim() == 'EXCLUIR';
    return reasonSelected && isConfirmValid;
  }

  DeleteAccountDB deleteUserDb = DeleteAccountDB();
  bool isLoadingDeleteAccount = false;

  Future<bool> deleteAccount() async {
    isLoadingDeleteAccount = true;
    notifyListeners();
    try {
      final userProfile = await getUserProfile();
      await deleteUserDb.saveDeletedUser(user: userProfile);

      final deleteSuccess = await deleteUserDb.deleteUser(
        email: userProfile.email!,
      );
      if (!deleteSuccess) {
        AppLogger.logError(
          'Falha ao deletar usuário do banco de dados',
          'Email: ${userProfile.email}',
          StackTrace.current,
        );
        throw Exception('Falha ao deletar usuário do banco de dados');
      }

      resetDeleteAccount();
      await clearUserData();
      await clearAccessToken();
      profileController.resetProfileState();
      await signOut();

      // Resetar primeiro acesso para que o usuário veja a página de login novamente
      await resetFirstAccess();

      baseController.index = 0;
      Modular.to.pushNamedAndRemoveUntil('/profile/login', (route) => false);
      isLoadingDeleteAccount = false;
      notifyListeners();
      return true;
    } catch (e) {
      isLoadingDeleteAccount = false;
      return false;
    }
  }

  void resetDeleteAccount() {
    selectedReason = null;
    optionalReasonController.clear();
    confirmController.clear();
  }

  Future<UserDeleteProfile> getUserProfile() async {
    final UserProfileModel? user = await _getUsuarios.getUserByEmail(
      Supabase.instance.client.auth.currentUser?.email ?? '',
    );

    UserDeleteProfile userProfileDelete = UserDeleteProfile(
      name: user?.name,
      email: user?.email,
      phone: user?.phone,
      image: user?.image,
      joinedDate: user?.joinedDate,
      accessToken: user?.accessToken,
      imageId: user?.imageId,
      tokenFirebase: user?.tokenFirebase,
      razonDelete: selectedReason,
      optionalReason: optionalReasonController.text,
    );
    return userProfileDelete;
  }

  /// Recupera as curtidas e produtos salvos do usuário após o login
  Future<void> _recoverUserLikesAndSavedProducts() async {
    try {
      final userEmail = supabase.auth.currentUser?.email;
      if (userEmail == null) return;

      AppLogger.logInfo('Recuperando curtidas e produtos salvos do usuário: $userEmail');

      // Recuperar produtos salvos
      await offersController.loadSavedProductIds();

      // Limpar curtidas locais e recarregar produtos para atualizar o estado das curtidas
      await offersController.removeAllLikedProductsOnlyApp();

      // Forçar recarregamento dos produtos para que os cards inicializem com o estado correto
      offersController.fetchNextProdutos();

      AppLogger.logInfo('Curtidas e produtos salvos recuperados com sucesso');
    } catch (e) {
      AppLogger.logError(
        'Erro ao recuperar curtidas e produtos salvos',
        e,
        StackTrace.current,
      );
    }
  }

  /// Verifica se o perfil do usuário está completo no banco de dados
  Future<bool> isProfileComplete() async {
    try {
      final userEmail = supabase.auth.currentUser?.email ?? '';
      if (userEmail.isEmpty) return false;

      final userProfile = await _getUsuarios.getUserByEmail(userEmail);

      return userProfile != null && userProfile.gender != null && userProfile.birthDate != null && userProfile.gender.toString().isNotEmpty && userProfile.name?.trim().isNotEmpty == true;
    } catch (e) {
      AppLogger.logError('Erro ao verificar se perfil está completo', e, StackTrace.current);
      return false; // Em caso de erro, assume perfil incompleto
    }
  }

  /// Verifica se é o primeiro acesso do usuário
  Future<bool> isFirstAccess() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('primeiroAcesso') ?? true;
  }

  /// Marca que o usuário já acessou o app pela primeira vez
  Future<void> markFirstAccessComplete() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('primeiroAcesso', false);
    AppLogger.logInfo('Primeiro acesso marcado como completo');
  }

  /// Reseta o primeiro acesso para que o usuário veja a página de login novamente
  Future<void> resetFirstAccess() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('primeiroAcesso', true);
    AppLogger.logInfo('Primeiro acesso resetado - usuário verá a página de login novamente');
  }

  /// Chama a RPC `anon_upsert_fcm_token`.
  /// - Se NÃO logado -> upsert em anon_fcm_tokens
  /// - Se logado -> remove o token de anon_fcm_tokens
  Future<void> upsertOrCleanupAnonToken() async {
    try {
      final token = await firebaseMessagingService.getToken() ?? '';
      if (token.isEmpty) {
        debugPrint('[anon_upsert_fcm_token] FCM token vazio, abortando.');
        return;
      }
      final info = await PackageInfo.fromPlatform();
      final platform = Platform.isIOS ? 'ios' : (Platform.isAndroid ? 'android' : 'web');

      final result = await supabase.rpc(
        'anon_upsert_fcm_token',
        params: {
          'p_token': token,
          'p_platform': platform,
          'p_app_version': info.version,
        },
      );
      debugPrint('[anon_upsert_fcm_token] ok -> $result');
    } on PostgrestException catch (e) {
      debugPrint('[anon_upsert_fcm_token] erro Postgrest: ${e.message}');
    } catch (e, st) {
      debugPrint('[anon_upsert_fcm_token] exception: $e\n$st');
    }
  }
}
