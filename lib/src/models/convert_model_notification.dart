import 'package:intl/intl.dart';
import 'notification_storage_service.dart';
import 'store_notifications_model.dart';

extension StoreNotificationsModelExtension on StoreNotificationsModel {
  NotificationStorageService toNotificationStorageService() {
    // Converta a data para um objeto DateTime
    String dateTimeString = date.toIso8601String();
    DateTime parsedDate = _parseDateTime(dateTimeString);

    return NotificationStorageService(
      id: id,
      title: title,
      body: message,
      date: parsedDate
          .toString(), // Mantenha a data como string ou use o formato desejado
      isRead: 0, // Defina como 0 ou o valor padrão desejado
      email: email,
      idProduto: idproduto,
    );
  }

  // Método privado para fazer a análise da data
  static DateTime _parseDateTime(String dateTimeString) {
    try {
      return DateFormat(
        'yyyy-MM-ddTHH:mm:ss',
      ).parse(dateTimeString); // Use o formato 'yyyy-MM-ddTHH:mm:ss'
    } catch (e) {
      return DateTime.now(); // Retorne a data atual em caso de erro na conversão
    }
  }
}
