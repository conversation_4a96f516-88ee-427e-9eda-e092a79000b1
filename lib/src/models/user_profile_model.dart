import 'dart:convert';

class UserProfileModel {
  String? name;
  String? surname;
  String? email;
  String? phone;
  String? image;
  String? imageId;
  String? accessToken;
  DateTime? joinedDate;
  DateTime? birthDate;
  int? gender; // 1= ela, 2= ele, 3= indefinido
  String? tokenFirebase;

  UserProfileModel({
    this.name,
    this.surname,
    this.email,
    this.phone,
    this.image,
    this.imageId,
    this.accessToken,
    this.joinedDate,
    this.birthDate,
    this.gender,
    this.tokenFirebase,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'surname': surname,
      'email': email,
      'phone': phone,
      'image': image,
      'image_id': imageId,
      'access_token': accessToken,
      'joined_date': joinedDate?.toIso8601String(),
      'birth_date': birthDate?.toIso8601String(),
      'gender': gender,
      'token_firebase': tokenFirebase,
    };
  }

  factory UserProfileModel.fromMap(Map<String, dynamic> map) {
    return UserProfileModel(
      name: map['name'] as String?,
      surname: map['surname'] as String?,
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      image: map['image'] as String?,
      imageId: map['image_id'] as String?,
      accessToken: map['access_token'] as String?,
      joinedDate: map['joined_date'] != null
          ? DateTime.parse(map['joined_date'].toString())
          : null,
      birthDate: map['birth_date'] != null
          ? DateTime.tryParse(map['birth_date'])
          : null,
      gender: map['gender'] as int?,
      tokenFirebase: map['token_firebase'] as String?,
    );
  }
  String toJson() => json.encode(toMap());

  factory UserProfileModel.fromJson(String source) =>
      UserProfileModel.fromMap(json.decode(source) as Map<String, dynamic>);

  factory UserProfileModel.empty() {
    return UserProfileModel(
      name: '',
      surname: '',
      email: '',
      phone: '',
      image: '',
      accessToken: '',
      joinedDate: null,
      birthDate: null,
      gender: null,
      tokenFirebase: '',
    );
  }
}
