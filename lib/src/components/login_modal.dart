import 'dart:io';
import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/src/modules/profile/ui/pages/login/onboarding_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'package:promobell/src/components/drag_indicator.dart';
import 'package:promobell/src/components/text_pattern.dart';
import 'package:promobell/src/modules/profile/controllers/login_controller.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/login_button.dart';
import 'package:promobell/src/modules/profile/ui/widgets/login/terms_and_privacy_text.dart';
import 'package:promobell/theme/color_outlet.dart';
import 'package:promobell/theme/svg_icons.dart';

class LoginModal extends StatefulWidget {
  const LoginModal({super.key, required this.text});
  final String text;

  @override
  State<LoginModal> createState() => _LoginModalState();
}

class _LoginModalState extends State<LoginModal> {
  final loginController = Modular.get<LoginController>();
  VoidCallback? _openContainer; // callback que o OpenContainer nos dá

  Future<void> _afterGoogle() async {
    await loginController.handleGoogleSignIn(context, fromModal: true);
    if (!mounted) return;

    if (Supabase.instance.client.auth.currentUser != null) {
      // Verifica se o perfil está completo antes de abrir o OpenContainer
      final isComplete = await loginController.isProfileComplete();
      if (!isComplete) {
        // Define que veio do modal APÓS login e abre o OpenContainer
        loginController.setOnboardingFromModal(true);
        _openContainer?.call(); // dispara a expansão do modal até fullscreen
      } else {
        // Se perfil está completo, fecha o modal (já navegou para home)
        if (mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
      }
    }
  }

  Future<void> _afterApple() async {
    await loginController.handleAppleSignIn(context, fromModal: true);
    if (!mounted) return;

    if (Supabase.instance.client.auth.currentUser != null) {
      // Verifica se o perfil está completo antes de abrir o OpenContainer
      final isComplete = await loginController.isProfileComplete();
      if (!isComplete) {
        // Define que veio do modal APÓS login e abre o OpenContainer
        loginController.setOnboardingFromModal(true);
        _openContainer?.call();
      } else {
        // Se perfil está completo, fecha o modal (já navegou para home)
        if (mounted && Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenH = MediaQuery.of(context).size.height;
    final kb = MediaQuery.of(context).viewInsets.bottom; // teclado
    final maxH = screenH * 0.92;

    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      behavior: HitTestBehavior.opaque,
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        child: Container(
          color: Colors.transparent,
          child: ConstrainedBox(
            constraints: BoxConstraints(maxHeight: maxH),
            child: OpenContainer<void>(
              tappable: false,
              openElevation: 0,
              closedElevation: 0,
              transitionDuration: const Duration(milliseconds: 620),
              transitionType: ContainerTransitionType.fade,
              closedColor: ColorOutlet.paper,
              openColor: ColorOutlet.paper,
              middleColor: ColorOutlet.paper,
              closedShape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
              ),
              openShape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.zero,
              ),
              openBuilder: (_, __) => const OnboardingPage(),
              closedBuilder: (_, openContainer) {
                _openContainer = openContainer;
                return SingleChildScrollView(
                  padding: EdgeInsets.only(bottom: kb),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const DragIndicator(),
                      Padding(
                        padding: const EdgeInsets.only(left: 32, right: 32),
                        child: SvgPicture.asset(SvgIcons.icon),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 24),
                            TextPattern.customText(
                              text: widget.text,
                              fontSize: 40,
                              fontWeightOption: FontWeightOption.bold,
                              color: ColorOutlet.contentPrimary,
                            ),
                            const SizedBox(height: 24),
                            TextPattern.customText(
                              text: 'Com a conta Promolover, você segue categorias e recebe alertas personalizados de produtos em oferta, cupons e frete grátis antes de todo mundo.',
                              fontSize: 14,
                              color: ColorOutlet.contentSecondary,
                            ),
                            const SizedBox(height: 24),

                            Visibility(
                              visible: !Platform.isIOS,
                              replacement: LoginButton(
                                icon: SvgIcons.brandsApple,
                                text: 'Continuar com Apple',
                                onPressed: loginController.isLoadingApple ? null : _afterApple,
                                isLoading: loginController.isLoadingApple,
                              ),
                              child: LoginButton(
                                icon: SvgIcons.brandsGoogle,
                                text: 'Continuar com Google',
                                onPressed: loginController.isLoading ? null : _afterGoogle,
                                isLoading: loginController.isLoading,
                              ),
                            ),

                            const SizedBox(height: 24),
                            SizedBox(
                              height: 56,
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed: () async {
                                  final navigator = Navigator.of(context);
                                  await loginController.upsertOrCleanupAnonToken();
                                  if (!mounted) return;

                                  navigator.pop();
                                },
                                child: TextPattern.customText(
                                  text: 'Continuar sem conta',
                                  color: ColorOutlet.contentTertiary,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            const TermsAndPrivacyText(),
                            const SizedBox(height: 42),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
