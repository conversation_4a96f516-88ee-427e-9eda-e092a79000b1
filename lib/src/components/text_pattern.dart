import 'package:flutter/material.dart';

import '../../theme/color_outlet.dart';

enum FontWeightOption {
  black,
  bold,
  extraBold,
  extraLight,
  light,
  medium,
  regular,
  semiBold,
  thin,
}

class TextPattern {
  static const String _fontFamily = 'Figtree';
  static const Color _defaultColor = ColorOutlet.contentSecondary;

  get defaultColor => _defaultColor;
  get fontFamily => _fontFamily;

  static Widget customText({
    required String text,
    double fontSize = 14,
    TextAlign textAlign = TextAlign.start,
    FontWeightOption fontWeightOption = FontWeightOption.regular,
    Color color = _defaultColor,
    double? lineHeight,
    int? maxLines,
    int? maxChars,
    bool? softWrap,
    TextOverflow? overflow,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
    FontStyle? fontStyle,
    bool isSelectable = false,
  }) {
    final calculatedLineHeight = lineHeight ?? _getLineHeight(fontSize, fontWeightOption);

    final textStyle = TextStyle(
      decoration: decoration,
      decorationColor: decorationColor,
      fontSize: fontSize,
      fontFamily: _fontFamily,
      color: color,
      fontWeight: _mapFontWeightOption(fontWeightOption),
      fontStyle: fontStyle,
      letterSpacing: 0,
      height: calculatedLineHeight,
      overflow: overflow,
      decorationStyle: decorationStyle,
    );

    final adjustedText = (maxChars != null && text.length > maxChars) ? '${text.substring(0, maxChars)}…' : text;

    if (isSelectable) {
      return SelectionArea(
        child: Text(
          adjustedText,
          textAlign: textAlign,
          maxLines: maxLines,
          softWrap: softWrap,
          style: textStyle,
        ),
      );
    } else {
      return Text(
        adjustedText,
        textAlign: textAlign,
        maxLines: maxLines,
        softWrap: softWrap,
        style: textStyle,
      );
    }
  }

  static double _getLineHeight(
    double fontSize,
    FontWeightOption fontWeightOption,
  ) {
    if (fontSize == 24 && fontWeightOption == FontWeightOption.bold) {
      return 1.25;
    }
    if (fontSize == 20 && fontWeightOption == FontWeightOption.bold) {
      return 1.25;
    }
    if (fontSize == 16) {
      return 1.5; // Aplica para todos os pesos com fontSize 16
    }
    if (fontSize == 14) {
      return 1.25; // Aplica para todos os pesos com fontSize 14
    }
    if (fontSize == 12) {
      return 1.0; // Aplica para todos os pesos com fontSize 12
    }
    if (fontSize == 11) {
      return 0.875; // Aplica para todos os pesos com fontSize 11
    }
    return 1.25;
  }

  static FontWeight _mapFontWeightOption(FontWeightOption option) {
    switch (option) {
      case FontWeightOption.black:
        return FontWeight.w900;
      case FontWeightOption.bold:
        return FontWeight.w700;
      case FontWeightOption.extraBold:
        return FontWeight.w800;
      case FontWeightOption.extraLight:
        return FontWeight.w200;
      case FontWeightOption.light:
        return FontWeight.w300;
      case FontWeightOption.medium:
        return FontWeight.w500;
      case FontWeightOption.semiBold:
        return FontWeight.w600;
      case FontWeightOption.thin:
        return FontWeight.w100;
      default:
        return FontWeight.w400;
    }
  }
}
