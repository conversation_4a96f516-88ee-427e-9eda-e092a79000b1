import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:promobell/src/components/text_pattern.dart';

class FlagDisabled extends StatelessWidget {
  final double? height;
  final String text;
  final double? width;
  final FontWeightOption fontWeightOption;

  const FlagDisabled({
    super.key,
    this.height = 48,
    this.text = 'Oferta indisponível na loja',
    this.width = double.infinity,
    this.fontWeightOption = FontWeightOption.semiBold,
  });

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.center,
      child: ClipRRect(
        child: SizedBox(
          height: height,
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
            child: Container(
              width: width,
              alignment: Alignment.center,
              color: Color.fromRGBO(255, 255, 255, 0.8),
              child: TextPattern.customText(
                text: text,
                fontSize: 14,
                fontWeightOption: fontWeightOption,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
