import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_shaders/flutter_shaders.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell/theme/svg_icons.dart';

const Color kBrandPrimary = Color(0xFF4141E1);

class FloatingWhatsButton extends StatefulWidget {
  final Offset initialOffset;
  final VoidCallback onTapOpenWhats;
  final double radius; // default 28 (56/2)
  final double intensity; // 0..1 – “força” da chama

  const FloatingWhatsButton({
    super.key,
    required this.initialOffset,
    required this.onTapOpenWhats,
    this.radius = 28,
    this.intensity = 0.9,
  });

  @override
  State<FloatingWhatsButton> createState() => _FloatingWhatsButtonState();
}

class _FloatingWhatsButtonState extends State<FloatingWhatsButton> with SingleTickerProviderStateMixin {
  late Offset _pos;
  bool _visible = true;

  // animação para iTime
  late final Ticker _ticker;
  double _time = 0.0;

  // tracking do gesto
  Offset _lastPanGlobalPos = Offset.zero;
  double _lastPanTs = 0; // s
  Offset _dragVelocity = Offset.zero;

  @override
  void initState() {
    super.initState();
    _pos = widget.initialOffset;
    _ticker = createTicker((elapsed) {
      _time = elapsed.inMilliseconds / 1000.0;
      if (mounted) setState(() {});
    })..start();
  }

  @override
  void dispose() {
    _ticker.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_visible) return const SizedBox.shrink();

    final size = MediaQuery.of(context).size;
    final center = Offset(_pos.dx + widget.radius, _pos.dy + widget.radius);

    return Stack(
      children: [
        // SHADER FULLSCREEN (efeito fogo roxo)
        Positioned.fill(
          child: IgnorePointer(
            child: ShaderBuilder(
              assetKey: 'assets/shaders/purple_flame.frag',
              (context, shader, _) {
                return CustomPaint(
                  painter: _FlameShaderPainter(
                    shader: shader,
                    time: _time,
                    resolution: size,
                    center: center,
                    velocity: _dragVelocity,
                    radius: widget.radius,
                    intensity: widget.intensity,
                  ),
                );
              },
            ),
          ),
        ),

        // Botão + close
        Positioned(
          left: _pos.dx,
          top: _pos.dy,
          child: SizedBox(
            width: widget.radius * 2 + 28, // área de toque maior
            height: widget.radius * 2 + 28,
            child: Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                // Glow roxo MUITO sutil (o brilho vem do shader)
                const DecoratedBox(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x1A4141E1), // 10%
                        blurRadius: 18,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: SizedBox(width: 64, height: 64),
                ),

                // Botão Whats
                GestureDetector(
                  onPanStart: (d) {
                    _lastPanGlobalPos = d.globalPosition;
                    _lastPanTs = _nowS();
                    setState(() {}); // atualiza uniforms já
                  },
                  onPanUpdate: (d) {
                    final now = _nowS();
                    final dt = (now - _lastPanTs).clamp(1 / 120.0, 0.05);
                    _dragVelocity = (d.globalPosition - _lastPanGlobalPos) / dt;
                    _lastPanGlobalPos = d.globalPosition;
                    _lastPanTs = now;

                    setState(() {
                      _pos = Offset(
                        (_pos.dx + d.delta.dx).clamp(0, size.width - widget.radius * 2),
                        (_pos.dy + d.delta.dy).clamp(0, size.height - widget.radius * 2),
                      );
                    });
                  },
                  onPanEnd: (_) {
                    _dragVelocity = Offset.zero;
                    setState(() {});
                  },
                  onTap: widget.onTapOpenWhats,
                  child: DecoratedBox(
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Color(0xFF22C55E), Color(0xFF16A34A)],
                      ),
                    ),
                    child: SizedBox(
                      width: widget.radius * 2,
                      height: widget.radius * 2,
                      child: Center(
                        child: SvgPicture.asset(
                          SvgIcons.brandsWhatsApp,
                          width: 26,
                          height: 26,
                          colorFilter: const ColorFilter.mode(
                            Colors.white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                // Close
                Positioned(
                  right: 0,
                  top: -6,
                  child: GestureDetector(
                    onTap: () => setState(() => _visible = false),
                    child: DecoratedBox(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.75),
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: const SizedBox(
                        width: 22,
                        height: 22,
                        child: Center(child: Icon(Icons.close, size: 14, color: Colors.white)),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  double _nowS() => DateTime.now().microsecondsSinceEpoch / 1e6;
}

class _FlameShaderPainter extends CustomPainter {
  final FragmentShader shader;
  final double time;
  final Size resolution;
  final Offset center;
  final Offset velocity;
  final double radius;
  final double intensity;

  _FlameShaderPainter({
    required this.shader,
    required this.time,
    required this.resolution,
    required this.center,
    required this.velocity,
    required this.radius,
    required this.intensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Passa uniforms (ordem = ordem das uniforms no shader)
    shader.setFloat(0, time); // uTime
    shader.setFloat(1, center.dx); // uCenter.x
    shader.setFloat(2, center.dy); // uCenter.y
    shader.setFloat(3, radius); // uRadius
    shader.setFloat(4, intensity); // uIntensity

    final paint = Paint()
      ..blendMode = BlendMode
          .plus // brilho aditivo sobre a UI
      ..shader = shader;

    canvas.drawRect(Offset.zero & size, paint);
  }

  @override
  bool shouldRepaint(covariant _FlameShaderPainter old) {
    return old.time != time || old.center != center || old.velocity != velocity || old.intensity != intensity || old.resolution != resolution;
  }
}
