import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:app_device_integrity/app_device_integrity.dart';
import 'package:crypto/crypto.dart';
import '../logs/app_logger.dart';

class AppIntegrityService {
  static final AppIntegrityService _instance = AppIntegrityService._internal();
  factory AppIntegrityService() => _instance;
  AppIntegrityService._internal();

  final AppDeviceIntegrity _appDeviceIntegrity = AppDeviceIntegrity();

  /// Gera um UUID de sessão único para iOS
  String _generateSessionId() {
    final random = Random.secure();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));

    // Converte para formato UUID
    final hex = bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return '${hex.substring(0, 8)}-${hex.substring(8, 12)}-${hex.substring(12, 16)}-${hex.substring(16, 20)}-${hex.substring(20, 32)}';
  }

  /// Gera um hash SHA-256 do sessionId para iOS
  String _generateChallengeHash(String sessionId) {
    final bytes = utf8.encode(sessionId);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Obtém o token de integridade do dispositivo
  ///
  /// Para Android: requer o GCP Project ID
  /// Para iOS: gera automaticamente um sessionId e challenge
  Future<Map<String, dynamic>> getIntegrityToken({
    required String gcpProjectId,
    String? customSessionId,
  }) async {
    try {
      AppLogger.logInfo('Iniciando verificação de integridade do dispositivo');

      if (Platform.isAndroid) {
        AppLogger.logInfo(
          'Plataforma: Android - GCP Project ID: $gcpProjectId',
        );

        if (gcpProjectId.isEmpty) {
          throw Exception('GCP Project ID é obrigatório para Android');
        }

        // Para Android, usamos um sessionId simples
        final sessionId = customSessionId ?? _generateSessionId();
        final gcpProjectIdInt = int.tryParse(gcpProjectId);

        if (gcpProjectIdInt == null) {
          throw Exception('GCP Project ID deve ser um número válido');
        }

        AppLogger.logInfo('Solicitando token de integridade para Android...');

        final token = await _appDeviceIntegrity.getAttestationServiceSupport(
          challengeString: sessionId,
          gcp: gcpProjectIdInt,
        );

        AppLogger.logInfo(
          'Token de integridade obtido com sucesso para Android',
        );

        return {
          'success': true,
          'platform': 'android',
          'token': token,
          'sessionId': sessionId,
          'gcpProjectId': gcpProjectId,
          'timestamp': DateTime.now().toIso8601String(),
        };
      } else if (Platform.isIOS) {
        AppLogger.logInfo('Plataforma: iOS');

        // Para iOS, geramos um sessionId único
        final sessionId = customSessionId ?? _generateSessionId();
        final challengeHash = _generateChallengeHash(sessionId);

        AppLogger.logInfo('Solicitando token de integridade para iOS...');
        AppLogger.logInfo('Session ID: $sessionId');

        final token = await _appDeviceIntegrity.getAttestationServiceSupport(
          challengeString: sessionId,
        );

        AppLogger.logInfo('Token de integridade obtido com sucesso para iOS');

        return {
          'success': true,
          'platform': 'ios',
          'token': token,
          'sessionId': sessionId,
          'challengeHash': challengeHash,
          'timestamp': DateTime.now().toIso8601String(),
        };
      } else {
        throw Exception(
          'Plataforma não suportada para verificação de integridade',
        );
      }
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao obter token de integridade',
        e.toString(),
        stackTrace,
      );

      return {
        'success': false,
        'error': e.toString(),
        'platform': Platform.isAndroid ? 'android' : 'ios',
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Verifica se o dispositivo suporta verificação de integridade
  Future<bool> isIntegritySupported() async {
    try {
      // Tenta uma verificação simples para ver se o serviço está disponível
      if (Platform.isAndroid || Platform.isIOS) {
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.logError(
        'Erro ao verificar suporte de integridade',
        e.toString(),
        StackTrace.current,
      );
      return false;
    }
  }

  /// Valida o formato do token recebido
  bool isValidToken(String? token) {
    if (token == null || token.isEmpty) {
      return false;
    }

    // Verifica se o token tem um tamanho mínimo esperado
    if (token.length < 100) {
      return false;
    }

    return true;
  }

  /// Cria um payload para enviar ao servidor de validação
  Map<String, dynamic> createServerPayload({
    required Map<String, dynamic> integrityResult,
    required String userEmail,
    String? additionalData,
  }) {
    return {
      'integrity': integrityResult,
      'user': {
        'email': userEmail,
        'timestamp': DateTime.now().toIso8601String(),
      },
      'metadata': {
        'appVersion': '1.0.1+2', // Pode ser obtido dinamicamente
        'platform': Platform.operatingSystem,
        'additionalData': additionalData,
      },
    };
  }
}
