import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/product.dart';
import '../../../logs/app_logger.dart';
import '../../connection/i_connection.dart';
import '../../db/db.dart';

class GetProdutos {
  final DB _db;

  GetProdutos({required IConnection connection, required DB db}) : _db = db;

  SupabaseClient supabase = Supabase.instance.client;

  Future<List<Product>> getProdutosPorCategoria(
    String categoriaNome, {
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      final data = await supabase.from(_db.tabelaDeProdutos).select().eq('categoria', categoriaNome).eq('ativo', true).order('id', ascending: false).range(offset, offset + limit - 1);

      final List<Product> produtos = data.map((produto) => Product.fromMap(produto)).toList();

      AppLogger.logInfo(
        'Produtos recuperados com sucesso: ${produtos.length}',
      );
      return produtos;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao buscar produtos por categoria',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  Future<List<Product>> getProdutosPorCategoriaAleatorios(
    String categoriaNome, {
    int limit = 30,
    int offset = 0,
  }) async {
    try {
      final data = await supabase.from(_db.tabelaDeProdutos).select().eq('categoria', categoriaNome).eq('ativo', true).order('id', ascending: false).range(offset, offset + limit - 1);

      final List<Product> produtos = data.map((produto) => Product.fromMap(produto)).toList();

      produtos.shuffle();

      AppLogger.logInfo(
        'Produtos recuperados com sucesso: ${produtos.length}',
      );

      return produtos;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao buscar produtos por categoria',
        e,
        stackTrace,
      );
      rethrow;
    }
  }

  Future<List<Product>> getAllProdutos({
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      final data = await supabase.from(_db.tabelaDeProdutos).select().eq('ativo', true).order('id', ascending: false).range(offset, offset + limit - 1);
      final List<Product> produtos = data.map((produto) => Product.fromMap(produto)).toList();

      AppLogger.logInfo(
        'Produtos recuperados com sucesso: ${produtos.length}',
      );
      return produtos;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produtos', e, stackTrace);
      rethrow;
    }
  }

  Future<Product> getProduto(int idProduto) async {
    try {
      final data = await supabase.from(_db.tabelaDeProdutos).select().eq('id', idProduto).eq('ativo', true).single();
      final Product produto = Product.fromMap(data);
      AppLogger.logInfo('Produto recuperado com sucesso: $produto');
      return produto;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar produto', e, stackTrace);
      rethrow;
    }
  }

  Future<int> getLikeCount(int productId) async {
    final response = await supabase.rpc(
      'count_likes',
      params: {'product_id_param': productId},
    );
    return response; // Retorna a contagem de curtidas
  }

  Future<bool> hasLikedProduct(
    int productId,
    String userEmail,
  ) async {
    try {
      final response = await supabase.rpc(
        'has_liked',
        params: {
          'product_id_param': productId,
          'user_email_param': userEmail,
        },
      );
      return response;
    } catch (e) {
      AppLogger.logError(
        'Erro ao verificar se o usuário curtiu o produto',
        e,
        StackTrace.current,
      );
      return false;
    }
  }

  Future<List<Product>> searchProdutos(String query) async {
    try {
      final response = await supabase.rpc(
        'buscar_produtos',
        params: {'query_input': query},
      );

      final List<Product> produtos = (response as List)
          .map(
            (item) => Product.fromMap(item as Map<String, dynamic>),
          )
          .toList();

      AppLogger.logInfo(
        'Produtos encontrados (RPC): ${produtos.length} para "$query"',
      );
      return produtos;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao buscar produtos via RPC',
        e,
        stackTrace,
      );
      return [];
    }
  }

  Future<int> getTotalProdutosPorCategoria(
    String categoriaNome,
  ) async {
    try {
      final result = await supabase.rpc(
        'get_total_produtos_por_categoria',
        params: {'categoria_nome': categoriaNome},
      );
      return result ?? 0;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao obter total de produtos da categoria "$categoriaNome"',
        e,
        stackTrace,
      );
      return 0;
    }
  }

  Future<List<Product>> buscarTopProdutosDetalhados({
    required int dias,
    required String categoria,
  }) async {
    final response = await supabase.rpc(
      'buscar_produtos_recentemente_adicionados',
      params: {'dias': dias, 'nome_categoria': categoria},
    );

    if (response == null) {
      throw Exception("Erro ao buscar produtos via RPC.");
    }

    final topProdutosData = List<Map<String, dynamic>>.from(
      response as List,
    );
    final ids = topProdutosData.map((e) => int.parse(e['id'].toString())).toList();

    if (ids.isEmpty) return [];

    final detalhesResponse = await supabase.from('produtos_cadastro').select().filter('id', 'in', '(${ids.join(',')})');

    final produtos = (detalhesResponse as List).map((item) => Product.fromMap(item)).toList();

    // Ordena conforme ordem original
    produtos.sort(
      (a, b) => ids.indexOf(a.id).compareTo(ids.indexOf(b.id)),
    );

    return produtos;
  }

  Future<List<Product>> getProdutosPorIdsComPaginacao({
    required List<int> ids,
    int limit = 10,
    int offset = 0,
  }) async {
    try {
      // Aplica paginação localmente nos IDs
      final paginatedIds = ids.skip(offset).take(limit).toList();

      if (paginatedIds.isEmpty) return [];

      // Constrói string para filtro IN: "(1,2,3)"
      final idsString = '(${paginatedIds.join(',')})';

      final data = await supabase.from(_db.tabelaDeProdutos).select().filter('id', 'in', idsString).eq('ativo', true);

      final produtos = (data as List)
          .map(
            (item) => Product.fromMap(item as Map<String, dynamic>),
          )
          .toList();

      // Ordena os produtos na mesma ordem dos IDs fornecidos
      produtos.sort(
        (a, b) => paginatedIds.indexOf(a.id).compareTo(paginatedIds.indexOf(b.id)),
      );

      return produtos;
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao buscar produtos por IDs com paginação',
        e,
        stackTrace,
      );
      return [];
    }
  }

  Future<List<int>> getSavedProductIds(String userEmail) async {
    final response = await supabase.from(_db.tabelaDeProdutosSalvos).select('product_id').eq('user_email', userEmail);
    return response.map<int>((e) => e['product_id'] as int).toList();
  }

  Future<List<Product>> buscarProdutosFiltrados({
    required String? ordenarPor,
    required bool? freteGratis,
    required double? precoMinimo,
    required double? precoMaximo,
    required List<String>? lojasSelecionadas,
    required List<int>? subcategoriasSelecionadas,
    required List<int>? categoriasSelecionadas,
    required int limit,
    required int offset,
  }) async {
    final data = await supabase.rpc(
      'buscar_produtos_filtrados',
      params: {
        'filtro_ordenacao': ordenarPor,
        'frete_gratis': null,
        'preco_min': precoMinimo,
        'preco_max': precoMaximo,
        'lojas': lojasSelecionadas,
        'subcategorias': subcategoriasSelecionadas,
        'categorias': categoriasSelecionadas,
        'limit_value': limit,
        'offset_value': offset,
      },
    );

    if (data == null) {
      throw Exception('Erro ao buscar produtos filtrados.');
    }

    return (data as List<dynamic>).map((e) => Product.fromMap(e)).toList();
  }

  Future<int> contarProdutosFiltrados({
    required bool? freteGratis,
    required double? precoMinimo,
    required double? precoMaximo,
    required List<String>? lojasSelecionadas,
    required List<int>? subcategoriasSelecionadas,
    required List<int>? categoriasSelecionadas,
  }) async {
    final todosNulosOuVazios = freteGratis == null && precoMinimo == null && precoMaximo == null && (lojasSelecionadas == null || lojasSelecionadas.isEmpty) && (subcategoriasSelecionadas == null || subcategoriasSelecionadas.isEmpty) && (categoriasSelecionadas == null || categoriasSelecionadas.isEmpty);

    if (todosNulosOuVazios) {
      return 0;
    }
    final count = await supabase.rpc(
      'contar_produtos_filtrados',
      params: {
        'frete_gratis': null,
        'preco_min': precoMinimo,
        'preco_max': precoMaximo,
        'lojas': lojasSelecionadas,
        'subcategorias': subcategoriasSelecionadas,
        'categorias': categoriasSelecionadas,
      },
    );

    if (count == null) {
      throw Exception('Erro ao contar produtos.');
    }

    return count as int;
  }
}
