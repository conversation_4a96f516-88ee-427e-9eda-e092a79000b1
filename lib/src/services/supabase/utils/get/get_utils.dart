import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/utils.dart';
import '../../../logs/app_logger.dart';
import '../../db/db.dart';

class GetUtils {
  SupabaseClient supabase = Supabase.instance.client;
  final DB db = DB();

  Future<Utils> getUtils() async {
    try {
      final data = await supabase.from(db.tabelaDeUtils).select().single();
      final Utils utils = Utils.fromMap(data);
      AppLogger.logInfo('Versão do app: ${utils.version}');
      return utils;
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao buscar tags', e, stackTrace);
      rethrow;
    }
  }
}
