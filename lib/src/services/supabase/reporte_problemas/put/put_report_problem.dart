import 'package:supabase_flutter/supabase_flutter.dart';
import '../../db/db.dart';

class PutReportProblem {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<void> insertProblem(int productId, String reason) async {
    final String userEmail = Supabase.instance.client.auth.currentUser!.email!;
    await _supabase.from(db.tabelaDeReportes).insert({
      'idProduct': productId,
      'emailUser': userEmail,
      'reason': reason,
    });
  }
}
