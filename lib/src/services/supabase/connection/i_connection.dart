abstract interface class IConnection {
  Future<List<dynamic>> get({
    required String table,
    Map<String, dynamic> filter = const {},
  });

  Future<List<dynamic>> post({
    required String table,
    required Map<String, dynamic> body,
    String? filter,
  });

  Future<List<dynamic>> put({
    required String table,
    required Map<String, dynamic> body,
    required Map<String, dynamic> filter,
  });

  Future<List<dynamic>> delete({
    required String table,
    required Map<String, dynamic> filter,
  });
}
