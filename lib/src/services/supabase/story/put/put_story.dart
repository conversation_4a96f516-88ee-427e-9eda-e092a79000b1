import 'package:supabase_flutter/supabase_flutter.dart';
import '../../db/db.dart';

class PutStory {
  final DB db = DB();
  final SupabaseClient supabase = Supabase.instance.client;

  Future<void> markStoryAsViewed(String userEmail, int productId) async {
    final supabase = Supabase.instance.client;

    await supabase.rpc(
      'mark_story_viewed',
      params: {'user_email_param': userEmail, 'product_id_param': productId},
    );
  }
}
