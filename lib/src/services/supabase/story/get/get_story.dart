import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../models/story_model.dart';
import '../../db/db.dart';

class GetStory {
  final DB db = DB();
  final SupabaseClient supabase = Supabase.instance.client;

  Future<List<StoryModel>> fetchStories(String userEmail) async {
    final supabase = Supabase.instance.client;

    final response = await supabase.rpc(
      'fetch_stories',
      params: {'user_email_param': userEmail},
    );

    if (response == null || response is List<dynamic> && response.isEmpty) {
      return [];
    }

    return (response as List<dynamic>)
        .map((data) => StoryModel.fromMap(data))
        .toList();
  }
}
