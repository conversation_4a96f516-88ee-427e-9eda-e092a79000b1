import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../logs/app_logger.dart';
import '../../../models/user_profile_model.dart';

class IntegrityValidationService {
  static final IntegrityValidationService _instance =
      IntegrityValidationService._internal();
  factory IntegrityValidationService() => _instance;
  IntegrityValidationService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;

  /// Envia o token de integridade para validação no servidor
  ///
  /// Este método pode ser usado para:
  /// 1. Armazenar o token para auditoria
  /// 2. Validar o token usando uma função Supabase Edge Function
  /// 3. Implementar políticas de segurança baseadas na integridade
  Future<Map<String, dynamic>> validateIntegrityToken({
    required Map<String, dynamic> integrityResult,
    required UserProfileModel user,
    String? additionalContext,
  }) async {
    try {
      AppLogger.logInfo(
        'Enviando token de integridade para validação no servidor',
      );

      final payload = {
        'integrity_data': integrityResult,
        'user_info': {
          'email': user.email,
          'name': user.name,
        },
        'metadata': {
          'timestamp': DateTime.now().toIso8601String(),
          'app_version': '1.0.1+2', // Pode ser obtido dinamicamente
          'additional_context': additionalContext,
        },
      };

      // Opção 1: Armazenar na tabela de auditoria
      await _storeIntegrityAudit(payload);

      // Opção 2: Chamar Edge Function para validação (se implementada)
      // final validationResult = await _callIntegrityValidationFunction(payload);

      AppLogger.logInfo('Token de integridade processado com sucesso');

      return {
        'success': true,
        'message': 'Token de integridade validado com sucesso',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      AppLogger.logError(
        'Erro ao validar token de integridade',
        e.toString(),
        StackTrace.current,
      );

      return {
        'success': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Armazena o token de integridade na tabela de auditoria
  Future<void> _storeIntegrityAudit(Map<String, dynamic> payload) async {
    try {
      // Cria uma tabela de auditoria se não existir
      // Esta tabela pode ser criada no Supabase Dashboard ou via SQL
      await _supabase.from('integrity_audit').insert({
        'user_email': payload['user_info']['email'],
        'platform': payload['integrity_data']['platform'],
        'token_hash': _generateTokenHash(payload['integrity_data']['token']),
        'session_id': payload['integrity_data']['sessionId'],
        'success': payload['integrity_data']['success'],
        'metadata': json.encode(payload['metadata']),
        'created_at': DateTime.now().toIso8601String(),
      });

      AppLogger.logInfo('Token de integridade armazenado na auditoria');
    } catch (e) {
      AppLogger.logError(
        'Erro ao armazenar token de integridade na auditoria',
        e.toString(),
        StackTrace.current,
      );
      // Não propaga o erro para não interromper o fluxo principal
    }
  }

  /// Gera um hash do token para armazenamento seguro
  String _generateTokenHash(String? token) {
    if (token == null || token.isEmpty) {
      return 'empty_token';
    }

    // Gera um hash simples para identificação (não armazena o token completo)
    return token.length > 50
        ? '${token.substring(0, 10)}...${token.substring(token.length - 10)}'
        : 'short_token_${token.length}';
  }

  /// Verifica se o usuário tem histórico de integridade válida
  Future<bool> hasValidIntegrityHistory(String userEmail) async {
    try {
      final response = await _supabase
          .from('integrity_audit')
          .select('success')
          .eq('user_email', userEmail)
          .eq('success', true)
          .order('created_at', ascending: false)
          .limit(1);

      return response.isNotEmpty;
    } catch (e) {
      AppLogger.logError(
        'Erro ao verificar histórico de integridade',
        e.toString(),
        StackTrace.current,
      );
      return false;
    }
  }

  /// Obtém estatísticas de integridade para um usuário
  Future<Map<String, dynamic>> getIntegrityStats(String userEmail) async {
    try {
      final response = await _supabase
          .from('integrity_audit')
          .select('success, platform, created_at')
          .eq('user_email', userEmail)
          .order('created_at', ascending: false)
          .limit(10);

      final total = response.length;
      final successful = response.where((r) => r['success'] == true).length;
      final platforms = response.map((r) => r['platform']).toSet().toList();

      return {
        'total_checks': total,
        'successful_checks': successful,
        'success_rate': total > 0 ? (successful / total * 100).round() : 0,
        'platforms_used': platforms,
        'last_check': response.isNotEmpty ? response.first['created_at'] : null,
      };
    } catch (e) {
      AppLogger.logError(
        'Erro ao obter estatísticas de integridade',
        e.toString(),
        StackTrace.current,
      );

      return {
        'total_checks': 0,
        'successful_checks': 0,
        'success_rate': 0,
        'platforms_used': [],
        'last_check': null,
      };
    }
  }
}
