import '../../../../models/user_profile_model.dart';
import '../../../exceptions/app_exception.dart';
import '../../../logs/app_logger.dart';
import '../../connection/i_connection.dart';
import '../../db/db.dart';

class PostUsuarios {
  final IConnection _connection;
  final DB _db;

  PostUsuarios({required IConnection connection, required DB db})
    : _connection = connection,
      _db = db;

  Future<UserProfileModel> createUser(UserProfileModel user) async {
    try {
      final data = await _connection.post(
        table: _db.tabelaDeUsuarios,
        body: {
          'name': user.name,
          'email': user.email,
          'phone': user.phone,
          'image': user.image,
          'image_id': user.imageId,
          'access_token': user.accessToken,
          'joined_date': DateTime.now().toIso8601String(),
          'token_firebase': user.tokenFirebase,
          'surname': user.surname,
          'gender': user.gender,
          'birth_date': user.birthDate,
        },
      );

      if (data.isEmpty) {
        throw DatabaseException(
          'Falha ao criar usuário: nenhum dado retornado',
        );
      }

      AppLogger.logInfo('Novo usuário criado com sucesso');
      return UserProfileModel.fromMap(data.first);
    } catch (e, stackTrace) {
      AppLogger.logError('Erro ao criar usuário', e, stackTrace);
      rethrow;
    }
  }
}
