import '../../../../models/user_profile_model.dart';
import '../../../logs/app_logger.dart';
import '../../connection/i_connection.dart';
import '../../db/db.dart';

class GetUsuarios {
  final IConnection _connection;
  final DB _db;

  GetUsuarios({required connection, required DB db})
    : _connection = connection,
      _db = db;

  Future<UserProfileModel?> getUserByEmail(String email) async {
    try {
      final data = await _connection.get(
        table: _db.tabelaDeUsuarios,
        filter: {'email': 'eq.$email'},
      );
      final userList = data
          .where((user) => (user as Map<String, dynamic>)['email'] == email)
          .toList();

      if (userList.isEmpty) {
        AppLogger.logInfo('Usuário não encontrado com email: $email');
        return null;
      }
      return UserProfileModel.fromMap(userList.first as Map<String, dynamic>);
    } catch (e, stackTrace) {
      AppLogger.logError(
        'Erro ao buscar usuário por email: $email',
        e,
        stackTrace,
      );
      rethrow;
    }
  }
}
