import '../../../../models/user_profile_model.dart';
import '../../../exceptions/app_exception.dart';
import '../../../logs/app_logger.dart';
import '../../connection/i_connection.dart';
import '../../db/db.dart';

class PutUsuarios {
  final IConnection _connection;
  final DB _db;

  PutUsuarios({required IConnection connection, required DB db}) : _connection = connection, _db = db;

  Future<UserProfileModel> updateUser(UserProfileModel user) async {
    try {
      final Map<String, dynamic> body = {};

      if (user.name?.trim().isNotEmpty ?? false) {
        body['name'] = user.name;
      }
      if (user.surname?.trim().isNotEmpty ?? false) {
        body['surname'] = user.surname;
      }
      if (user.image?.trim().isNotEmpty ?? false) {
        body['image'] = user.image;
      }
      if (user.imageId?.trim().isNotEmpty ?? false) {
        body['image_id'] = user.imageId;
      }
      if (user.tokenFirebase?.trim().isNotEmpty ?? false) {
        body['token_firebase'] = user.tokenFirebase;
      }
      if (user.birthDate != null) {
        body['birth_date'] = user.birthDate!.toIso8601String();
      }
      if (user.gender?.toString().trim().isNotEmpty ?? false) {
        body['gender'] = user.gender;
      }

      final data = await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: body,
        filter: {'email': user.email},
      );

      if (data.isEmpty) {
        // Tenta buscar o usuário para verificar se existe
        final existingUser = await _connection.get(
          table: _db.tabelaDeUsuarios,
          filter: {'email': user.email},
        );

        if (existingUser.isEmpty) {
          throw DatabaseException('Usuário não encontrado para atualização');
        } else {
          throw DatabaseException('Falha ao atualizar usuário - dados não retornados');
        }
      }

      return UserProfileModel.fromMap(data.first);
    } catch (e, stack) {
      AppLogger.logError('Erro ao atualizar usuário', e, stack);
      rethrow;
    }
  }

  Future<UserProfileModel> updateDataUser({
    required String email,
    required String name,
    required String surname,
    required int gender,
    required DateTime birthDate,
  }) async {
    try {
      final data = await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {
          'name': name,
          'surname': surname,
          'gender': gender,
          'birth_date': birthDate.toIso8601String(),
        },
        filter: {'email': email},
      );

      if (data.isEmpty) {
        throw DatabaseException('Falha ao atualizar usuário');
      }

      return UserProfileModel.fromMap(data.first);
    } catch (e, stack) {
      AppLogger.logError('Erro ao atualizar usuário', e, stack);
      rethrow;
    }
  }

  Future<void> updateUserImageMapping(
    String email,
    String imageId,
  ) async {
    try {
      await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {'image_id': imageId},
        filter: {'email': email},
      );
    } catch (e, stack) {
      AppLogger.logError(
        'Erro ao atualizar mapeamento de imagem',
        e,
        stack,
      );
      rethrow;
    }
  }

  Future<void> postTokenFCM({
    required String token,
    required String email,
  }) async {
    try {
      await _connection.put(
        table: _db.tabelaDeUsuarios,
        body: {'token_firebase': token},
        filter: {'email': email},
      );
    } catch (e, stack) {
      AppLogger.logError('Error no postTokenFCM:', e, stack);
      rethrow;
    }
  }
}
