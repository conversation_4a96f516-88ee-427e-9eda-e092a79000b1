import '../../db/db.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PutCategorias {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<void> followCategory(
    int categoryId,
    String userEmail,
    String categoryName,
  ) async {
    final response = await _supabase.from(db.tabelaDeProdutosFavoritos).insert({
      'category_id': categoryId, // ID da categoria que o usuário está seguindo
      'user_email': userEmail, // Email do usuário autenticado
      'category_nome':
          categoryName, // Nome da categoria que o usuário está seguindo
    });

    if (response != null) {
      throw Exception('Erro ao seguir a categoria: $response');
    }
  }
}
