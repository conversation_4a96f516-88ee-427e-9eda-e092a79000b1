import '../../../logs/app_logger.dart';
import '../../db/db.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class DeleteCategorias {
  final SupabaseClient _supabase = Supabase.instance.client;
  DB db = DB();

  Future<void> unFollowCategory(
    int categoryId,
    String userEmail,
    String categoria,
  ) async {
    try {
      await _supabase.from(db.tabelaDeProdutosFavoritos).delete().match({
        'category_id': categoryId,
        'user_email': userEmail,
        'category_nome': categoria,
      });
    } catch (e) {
      AppLogger.logError(
        'Erro ao descurtir a categoria',
        e,
        StackTrace.current,
      );
    }
  }
}
