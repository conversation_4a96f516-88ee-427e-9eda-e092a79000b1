import 'package:firebase_analytics/firebase_analytics.dart';

import 'logs/app_logger.dart';

class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Rastrear visualização de produto
  static Future<void> trackProductView({
    required String productId,
    required String productName,
    required String category,
    required double price,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'view_item',
        parameters: {
          'item_id': productId,
          'item_name': productName,
          'item_category': category,
          'value': price,
          'currency': 'BRL',
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear visualização de produto: $e');
    }
  }

  // Rastrear clique em produto
  static Future<void> trackProductClick({
    required String productId,
    required String productName,
    required String category,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'select_content',
        parameters: {
          'content_type': 'product',
          'item_id': productId,
          'item_name': productName,
          'item_category': category,
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear clique em produto: $e');
    }
  }

  // Rastrear compartilhamento
  static Future<void> trackShare({
    required String method,
    required String contentType,
    required String itemId,
    String? itemName,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'share',
        parameters: {
          'method': method,
          'content_type': contentType,
          'item_id': itemId,
          if (itemName != null) 'item_name': itemName,
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear compartilhamento: $e');
    }
  }

  // Rastrear clique em link externo
  static Future<void> trackExternalLinkClick({
    required String url,
    required String source,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'click_external_link',
        parameters: {
          'link_url': url,
          'source': source,
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear clique em link externo: $e');
    }
  }

  // Rastrear visualização de página
  static Future<void> trackPageView({
    required String pageName,
    String? pageTitle,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: pageName,
        screenClass: pageTitle ?? pageName,
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear visualização de página: $e');
    }
  }

  // Rastrear busca
  static Future<void> trackSearch({
    required String searchTerm,
    String? category,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'search',
        parameters: {
          'search_term': searchTerm,
          if (category != null) 'category': category,
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear busca: $e');
    }
  }

  // Rastrear download de app
  static Future<void> trackAppDownload({
    required String source,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'app_download',
        parameters: {
          'source': source,
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear download de app: $e');
    }
  }

  // Rastrear erro
  static Future<void> trackError({
    required String errorMessage,
    String? errorCode,
    String? screen,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'app_error',
        parameters: {
          'error_message': errorMessage,
          if (errorCode != null) 'error_code': errorCode,
          if (screen != null) 'screen': screen,
        },
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao rastrear erro: $e');
    }
  }

  // Definir propriedades do usuário
  static Future<void> setUserProperty({
    required String name,
    required String value,
  }) async {
    try {
      await _analytics.setUserProperty(
        name: name,
        value: value,
      );
    } catch (e) {
      AppLogger.logInfo('Erro ao definir propriedade do usuário: $e');
    }
  }

  // Definir ID do usuário
  static Future<void> setUserId(String userId) async {
    try {
      await _analytics.setUserId(id: userId);
    } catch (e) {
      AppLogger.logInfo('Erro ao definir ID do usuário: $e');
    }
  }
}
