import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:lottie/lottie.dart';
import 'package:vibration/vibration.dart';

import '../../theme/color_outlet.dart';
import '../modules/offers/controllers/offers_controller.dart';
import '../modules/offers/controllers/story/story_controller.dart';
import '../modules/profile/controllers/login_controller.dart';
import 'base/controllers/base_controller/base_controller.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final controller = Modular.get<OffersController>();
  final baseController = Modular.get<BaseController>();
  final storyController = Modular.get<StoryController>();
  final loginController = Modular.get<LoginController>();

  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    _startVibrationGradually();
    _checkAuth();
  }

  Future<void> _startVibrationGradually() async {
    final hasVibrator = await Vibration.hasVibrator();
    if (!(hasVibrator)) return;

    // Início: leve toque ao começar a splash
    await Future.delayed(const Duration(milliseconds: 500));
    Vibration.vibrate(duration: 100); // leve expectativa

    // 1º sino (entrada com tremor)
    await Future.delayed(const Duration(milliseconds: 400));
    Vibration.vibrate(duration: 300);
    await Future.delayed(const Duration(milliseconds: 100));
    Vibration.vibrate(duration: 200);

    // 2º sino (agitação aumentando)
    await Future.delayed(const Duration(milliseconds: 200));
    Vibration.vibrate(duration: 400);
    await Future.delayed(const Duration(milliseconds: 100));
    Vibration.vibrate(duration: 300);

    // 3º sino (pico da trepidação)
    await Future.delayed(const Duration(milliseconds: 150));
    Vibration.vibrate(duration: 500);
    await Future.delayed(const Duration(milliseconds: 80));
    Vibration.vibrate(duration: 250);

    // Toque final com impacto prolongado
    await Future.delayed(const Duration(milliseconds: 300));
    Vibration.vibrate(duration: 600); // vibração dramática
  }

  Future<void> _checkAuth() async {
    storyController.getAllStories();

    final isFirstAccess = await loginController.isFirstAccess();

    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;

    String nextRoute;

    if (isFirstAccess) {
      // Primeira vez abrindo o app - vai para login
      nextRoute = '/profile/login';
    } else {
      // Não é primeira vez - vai direto para home se tiver token, senão home mesmo
      nextRoute = '/home';
    }

    // Inicia a navegação
    Modular.to.navigate(nextRoute);

    // Aguarda um pouco para dar tempo da navegação ocorrer visualmente
    await Future.delayed(const Duration(milliseconds: 30));
    if (mounted) {
      setState(() {
        _isVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorOutlet.contentPrimary,
      body: Center(
        child: Visibility(
          visible: _isVisible,
          child: Lottie.asset(
            'assets/lottie/lottie-logo.json',
            width: 120,
            height: 120,
            fit: BoxFit.cover,
            repeat: true,
            animate: true,
          ),
        ),
      ),
    );
  }
}
