import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell/src/services/supabase/usuarios/put/put_usuarios.dart';

import '../../../supa_base_config_keys.dart';
import '../../modules/categories/controllers/categories_controller.dart';
import '../../modules/offers/controllers/notification_controller.dart';
import '../../modules/offers/controllers/offers_controller.dart';
import '../../modules/offers/controllers/products_details_controller.dart';
import '../../modules/offers/controllers/story/story_animation_controller.dart';
import '../../modules/offers/controllers/story/story_controller.dart';
import '../../modules/offers/controllers/story/story_navigation_controller.dart';
import '../../modules/profile/controllers/login_controller.dart';
import '../../modules/profile/controllers/profile_controller.dart';
import '../../services/digital_ocean/connection/i_storage_connection.dart';
import '../../services/navigation/scroll_services.dart';
import '../../services/supabase/produtos/get/get_produtos.dart';
import '../../services/supabase/usuarios/get/get_usuarios.dart';
import '../../services/supabase/usuarios/post/post_usuarios.dart';
import '../base/controllers/base_controller/base_controller.dart';
import 'services_module.dart';

class ControllersModule extends Module {
  @override
  List<Module> get imports => [ServicesModule()];

  @override
  void exportedBinds(i) {
    i.addSingleton(
      () => BaseController(
        putUsuarios: i.get<PutUsuarios>(),
        scrollService: i.get<ScrollService>(),
      ),
    );
    i.addSingleton(NotificationController.new);
    i.addLazySingleton(
      () => LoginController(
        postUsuarios: i.get<PostUsuarios>(),
        getUsuarios: i.get<GetUsuarios>(),
        supaBaseConfigKeys: i.get<SupaBaseConfigKeys>(),
      ),
    );
    i.addSingleton(
      () => ProfileController(
        getUsuarios: i.get<GetUsuarios>(),
        storageConnection: i.get<IStorageConnection>(),
        putUsuarios: i.get<PutUsuarios>(),
      ),
    );
    i.addSingleton(
      () => OffersController(getProdutos: i.get<GetProdutos>()),
    );
    i.addSingleton(() => StoryController());
    i.add<ProductDetailsController>(ProductDetailsController.new);
    i.add<StoryAnimationController>(StoryAnimationController.new);
    i.add<StoryNavigationController>(StoryNavigationController.new);
    i.add<CategoriesController>(CategoriesController.new);
  }
}
