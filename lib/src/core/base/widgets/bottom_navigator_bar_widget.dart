import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../theme/color_outlet.dart';
import '../../../../theme/svg_icons.dart';
import '../../../modules/offers/controllers/offers_controller.dart';
import '../../../modules/profile/controllers/profile_controller.dart';
import '../../../services/navigation/scroll_services.dart';
import '../controllers/base_controller/base_controller.dart';
import 'icon_bottom_bar.dart';

class BottomNavigationBarWidget extends StatefulWidget {
  final Color? color;
  final bool isCategory;
  final void Function(int index)? onTap;
  const BottomNavigationBarWidget({
    super.key,
    this.color = const Color(0xFFFAFAFC),
    this.isCategory = false,
    this.onTap,
  });

  @override
  State<BottomNavigationBarWidget> createState() =>
      _BottomNavigationBarWidgetState();
}

class _BottomNavigationBarWidgetState extends State<BottomNavigationBarWidget> {
  final controller = Modular.get<BaseController>();
  final profileController = Modular.get<ProfileController>();
  final offersController = Modular.get<OffersController>();
  final scrollService = Modular.get<ScrollService>();

  @override
  void initState() {
    super.initState();
    profileController.loadUserData();
  }

  void _handleNavigation(int index) {
    final currentPath = Modular.to.path;

    if (currentPath == '/home') {
      if (controller.index == index) {
        scrollService.scrollToTop(getPageKeyForIndex(index));
      }
      controller.navPage(index);
    } else {
      Modular.to.navigate('/home');
      controller.navPage(index);
    }

    if (index > 0) {
      offersController.setFilter("Recentes");
    }
  }

  String getPageKeyForIndex(int index) {
    switch (index) {
      case 0:
        return 'offersPage';
      case 1:
        return 'categoriesPage';
      case 2:
        return 'profilePage';
      default:
        return 'offersPage';
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            // Sombra apenas na parte inferior
            Positioned(
              left: 16.0,
              right: 16.0,
              bottom: 0.0, // Posiciona a sombra um pouco abaixo
              child: Container(
                height: 20.0, // Altura da sombra
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.vertical(
                    bottom: Radius.circular(24),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(
                        0xFF313152,
                      ).withValues(alpha: 0.1),
                      blurRadius: 16.0,
                      spreadRadius: 0.0,
                      offset: const Offset(0, 16),
                    ),
                  ],
                ),
              ),
            ),

            // BottomNavigationBar principal com Blur e ClipRRect
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(24)),
                child: BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 24.0,
                    sigmaY: 24.0,
                  ),
                  child: Container(
                    height: 88,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      color: widget.color!.withValues(alpha: 0.6),
                      border: Border.all(
                        color: ColorOutlet.paper,
                        width: 1.0,
                      ),
                    ),
                    child: AnimatedBuilder(
                      animation: Listenable.merge([
                        controller,
                        profileController,
                      ]),
                      builder: (context, child) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Expanded(
                              child: IconBottomBar(
                                svg: controller.index == 0
                                    ? SvgIcons.financeDiscountFilled
                                    : SvgIcons.financeDiscount,
                                label: 'Ofertas',
                                currentIndex: 0,
                                onTap: () async {
                                  _handleNavigation(0);
                                  offersController.searchQuery = '';
                                  offersController
                                      .limparProdutosFiltradosByUser();
                                  offersController.setTipoLista(
                                    TipoListaProdutos.geral,
                                  );
                                  offersController.selectedFilter = 'Recentes';

                                  await offersController.refreshProdutos();
                                  offersController.searchQuery = '';
                                  offersController.limparTodosOsFiltros(
                                    isCategoryFilters: false,
                                  );
                                },
                              ),
                            ),
                            const SizedBox(width: 16.0),
                            Expanded(
                              child: IconBottomBar(
                                svg: controller.index == 1
                                    ? SvgIcons.actionNavigationCategoryFilled
                                    : SvgIcons.actionNavigationCategory,
                                label: 'Categorias',
                                currentIndex: 1,
                                onTap: () {
                                  _handleNavigation(1);
                                  offersController.setFilter(
                                    "Recentes",
                                  );
                                  offersController.setFilterCategory(
                                    "Recentes",
                                  );
                                  offersController.searchQuery = '';
                                  offersController.limparTodosOsFiltros(
                                    isCategoryFilters: false,
                                  );
                                },
                              ),
                            ),
                            const SizedBox(width: 16.0),
                            Expanded(
                              child: IconBottomBar(
                                svg: controller.index == 2
                                    ? SvgIcons.markerVerifiedFilled
                                    : SvgIcons.markerVerified,
                                label: 'Conta',
                                currentIndex: 2,
                                onTap: () {
                                  _handleNavigation(2);
                                  offersController.setFilter(
                                    "Recentes",
                                  );
                                  offersController.setFilterCategory(
                                    "Recentes",
                                  );
                                  offersController.searchQuery = '';
                                  offersController.limparTodosOsFiltros(
                                    isCategoryFilters: false,
                                  );
                                },
                              ),
                            ),
                            const SizedBox(width: 16.0),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
