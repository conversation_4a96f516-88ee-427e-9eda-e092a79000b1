#version 100
precision highp float;
#include <flutter/runtime_effect.glsl>

/* uniforms — mantenha a MESMA ordem no setFloat() */
uniform float uTime;         // segundos
uniform vec2  uCenter;       // centro do botão (px)
uniform float uRadius;       // raio do botão (px)
uniform float uIntensity;    // 0..1

/* --- simplex noise (compacto) --- */
vec3 mod289(vec3 x){ return x - floor(x * (1.0/289.0)) * 289.0; }
vec2 mod289(vec2 x){ return x - floor(x * (1.0/289.0)) * 289.0; }
vec3 permute(vec3 x){ return mod289(((x*34.0)+1.0)*x); }
float snoise(vec2 v){
  const vec4 C = vec4(0.211324865405187, 0.366025403784439,
                      -0.577350269189626, 0.024390243902439);
  vec2 i = floor(v + dot(v, C.yy));
  vec2 x0 = v - i + dot(i, C.xx);
  vec2 i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
  vec4 x12 = x0.xyxy + C.xxzz;
  x12.xy -= i1;
  i = mod289(i);
  vec3 p = permute(permute(i.y + vec3(0.0, i1.y, 1.0)) + i.x + vec3(0.0, i1.x, 1.0));
  vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
  m = m*m; m = m*m;
  vec3 x = 2.0*fract(p*vec3(0.024390243902439))-1.0; // p*C.www
  vec3 h = abs(x)-0.5;
  vec3 ox = floor(x+0.5);
  vec3 a0 = x - ox;
  m *= 1.79284291400159 - 0.85373472095314 * (a0*a0 + h*h); // <- linha corrigida
  vec3 g;
  g.x  = a0.x * x0.x  + h.x  * x0.y;
  g.yz = a0.yz*x12.xz + h.yz*x12.yw;
  return 130.0 * dot(m, g);
}
float fbm(vec2 p){
  float a = 0.5, f = 0.0;
  for (int i=0; i<5; i++){ f += a * snoise(p); p *= 2.07; a *= 0.55; }
  return f;
}

/* paleta roxa: #4141E1 -> #6C6CF2 -> #A5A5FF */
vec3 brandRamp(float t){
  vec3 c0 = vec3(0x41,0x41,0xE1)/255.0;
  vec3 c1 = vec3(0x6C,0x6C,0xF2)/255.0;
  vec3 c2 = vec3(0xA5,0xA5,0xFF)/255.0;
  vec3 m = mix(c0, c1, smoothstep(0.0, 0.55, t));
  return mix(m, c2, smoothstep(0.55, 1.0, t));
}

/* ===== Entrada para flutter_shaders: RETORNA a cor ===== */
half4 main(vec2 fragCoord) {
  vec2  p   = fragCoord - uCenter;
  float r   = length(p);                 // raio
  float th  = atan(p.y, p.x);            // ângulo [-pi..pi]
  float ra  = r - uRadius;               // distância para fora da borda

  // parâmetros visuais (ajuste fino)
  float flameMax   = 36.0;               // altura média (px)
  float tongueAmp  = 18.0;               // variação (px)
  float falloff    = 18.0;               // suavidade (px)
  float freq       = 7.0;                // quantidade de línguas
  float tAnim      = uTime*0.5;          // velocidade da animação

  // ruído em coordenada ANGULAR (ancorado ao botão)
  float n = fbm(vec2(th*freq, tAnim + sin(th*2.0)*0.1));
  n = (n*0.5 + 0.5);                     // 0..1

  // altura da chama naquele ângulo
  float H = flameMax + tongueAmp * n;

  // máscara da chama (1 na borda, 0 fora)
  float flame = 1.0 - smoothstep(H, H + falloff, ra);
  flame *= smoothstep(-6.0, 4.0, max(ra, -6.0)); // não invade o botão
  flame = clamp(flame, 0.0, 1.0);

  // inner glow colado no botão
  float inner = 1.0 - smoothstep(2.0, 14.0, max(0.0, ra));
  inner *= 0.35;

  // cor + alpha (pré-multiplicado)
  float t = clamp(inner + flame, 0.0, 1.0);
  vec3 col = brandRamp(0.35 + 0.65*t);
  float alpha = clamp((inner + flame) * (0.85 + 0.15*n), 0.0, 1.0) * uIntensity;

  return vec4(col * alpha, alpha);
}
