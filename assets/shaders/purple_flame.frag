#version 100
precision highp float;
#include <flutter/runtime_effect.glsl>

/* ---- Uniforms (ordem deve bater com setFloat no Dart) ---- */
uniform vec2  uCanvasSize;   // (width, height)
uniform float uTime;         // seconds
uniform vec2  uCenter;       // button center (px)
uniform vec2  uVelocity;     // drag velocity (px/s)
uniform float uRadius;       // button radius (px)
uniform float uIntensity;    // 0..1

/* ---- Simplex noise + FBM (compacto) ---- */
vec3 mod289(vec3 x){ return x - floor(x * (1.0/289.0)) * 289.0; }
vec2 mod289(vec2 x){ return x - floor(x * (1.0/289.0)) * 289.0; }
vec3 permute(vec3 x){ return mod289(((x*34.0)+1.0)*x); }
float snoise(vec2 v){
  const vec4 C = vec4(0.211324865405187, 0.366025403784439,
                      -0.577350269189626, 0.024390243902439);
  vec2 i = floor(v + dot(v, C.yy));
  vec2 x0 = v - i + dot(i, C.xx);
  vec2 i1 = (x0.x > x0.y) ? vec2(1.0, 0.0) : vec2(0.0, 1.0);
  vec4 x12 = x0.xyxy + C.xxzz;
  x12.xy -= i1;
  i = mod289(i);
  vec3 p = permute(permute(i.y + vec3(0.0, i1.y, 1.0)) + i.x + vec3(0.0, i1.x, 1.0));
  vec3 m = max(0.5 - vec3(dot(x0,x0), dot(x12.xy,x12.xy), dot(x12.zw,x12.zw)), 0.0);
  m = m*m; m = m*m;
  vec3 x = 2.0*fract(p*vec3(0.024390243902439))-1.0;
  vec3 h = abs(x)-0.5;
  vec3 ox = floor(x+0.5);
  vec3 a0 = x - ox;
  m *= 1.79284291400159 - 0.85373472095314*(a0*a0+h*h);
  vec3 g;
  g.x  = a0.x * x0.x  + h.x  * x0.y;
  g.yz = a0.yz*x12.xz + h.yz*x12.yw;
  return 130.0 * dot(m, g);
}
float fbm(vec2 p){
  float a = 0.5;
  float f = 0.0;
  for (int i=0; i<5; i++){
    f += a * snoise(p);
    p *= 2.07;
    a *= 0.55;
  }
  return f;
}

/* ---- Rampa de cor roxa ---- */
vec3 brandRamp(float t){
  vec3 c0 = vec3(0x41,0x41,0xE1)/255.0; // #4141E1
  vec3 c1 = vec3(0x6C,0x6C,0xF2)/255.0; // #6C6CF2
  vec3 c2 = vec3(0xA5,0xA5,0xFF)/255.0; // #A5A5FF
  vec3 m = mix(c0, c1, smoothstep(0.0, 0.6, t));
  return mix(m, c2, smoothstep(0.6, 1.0, t));
}

/* ===== Entrada para flutter_shaders: RETORNA a cor ===== */
half4 main(vec2 fragCoord) {
  vec2 uv   = fragCoord;
  vec2 toP  = uv - uCenter;
  float dist = length(toP);
  vec2 dir  = (dist > 0.0) ? toP / dist : vec2(0.0);

  // vento = oposto ao arraste
  vec2  v    = uVelocity;
  float vlen = length(v) + 1e-4;
  vec2  wind = (vlen > 1.0) ? normalize(-v) : vec2(0.0, -1.0);

  // alinhamento + ruído
  float along = dot(dir, wind);
  float edge  = smoothstep(uRadius, uRadius + 8.0, dist);

  vec2  npos = uv*0.02 + wind * (uTime*1.2 + vlen*0.002);
  float turb = fbm(npos + dir*0.8);

  float lenBoost = clamp(vlen/800.0, 0.0, 1.2);
  float core  = smoothstep(0.0, 0.9, along*0.7 + turb*0.9 - edge*1.1 + lenBoost*0.5);
  float plume = smoothstep(0.2, 1.0, along*0.9 + turb*0.6 - edge*0.8 + lenBoost*0.7);

  float flame = max(core, plume) * uIntensity;

  // suaviza perto do botão
  float fadeNear = smoothstep(uRadius-2.0, uRadius+10.0, dist);
  flame *= fadeNear;

  float t = clamp(0.25 + 0.75*flame, 0.0, 1.0);
  vec3 col = brandRamp(t);

  float alpha = clamp(flame, 0.0, 1.0);
  return vec4(col * alpha, alpha); // pré-multiplicado (use BlendMode.plus)
}
