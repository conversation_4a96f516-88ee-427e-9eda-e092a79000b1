package br.com.promobell

import io.flutter.embedding.android.FlutterActivity
import android.os.Bundle
import android.os.Build
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import android.graphics.Color
import android.content.Intent
import android.net.Uri
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "br.com.promobell/app_links"
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Habilita edge-to-edge para compatibilidade com Android 15+
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // Configura as cores das barras de sistema usando APIs modernas
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = Color.TRANSPARENT
            window.navigationBarColor = Color.TRANSPARENT
            
            // Para Android 15+, usa WindowInsetsController para melhor controle
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                val controller = WindowInsetsControllerCompat(window, window.decorView)
                controller.isAppearanceLightStatusBars = false
                controller.isAppearanceLightNavigationBars = false
            }
        }
        
        // Tratar intent inicial
        handleIntent(intent)
    }
    
    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }
    
    private fun handleIntent(intent: Intent) {
        val appLinkAction = intent.action
        val appLinkData: Uri? = intent.data
        
        if (Intent.ACTION_VIEW == appLinkAction && appLinkData != null) {
            // Configurar MethodChannel para comunicar com Flutter
            flutterEngine?.dartExecutor?.binaryMessenger?.let { messenger ->
                val channel = MethodChannel(messenger, CHANNEL)
                
                when {
                    appLinkData.path?.startsWith("/product") == true -> {
                        val productId = appLinkData.getQueryParameter("id")
                        channel.invokeMethod("navigateToProduct", mapOf("id" to productId))
                    }
                    appLinkData.path?.startsWith("/category") == true -> {
                        val categoryId = appLinkData.getQueryParameter("id")
                        channel.invokeMethod("navigateToCategory", mapOf("id" to categoryId))
                    }
                }
            }
        }
    }
}
