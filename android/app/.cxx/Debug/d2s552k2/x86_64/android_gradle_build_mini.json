{"buildFiles": ["H:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["H:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\projetos\\chegou mercado\\promobell\\android\\app\\.cxx\\Debug\\d2s552k2\\x86_64", "clean"]], "buildTargetsCommandComponents": ["H:\\Android\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "H:\\projetos\\chegou mercado\\promobell\\android\\app\\.cxx\\Debug\\d2s552k2\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}