{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "H:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "H:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "H:/Android/cmake/3.22.1/bin/ctest.exe", "root": "H:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-ed960b02a45bdc14ab93.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-03b48229f043f2eb4c06.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-a09410a902b62282d845.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-03b48229f043f2eb4c06.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-a09410a902b62282d845.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ed960b02a45bdc14ab93.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}