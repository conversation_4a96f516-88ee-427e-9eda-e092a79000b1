{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "H:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "H:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "H:/Android/cmake/3.22.1/bin/ctest.exe", "root": "H:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-9c34d6c248976851229f.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-71cb3f8bebcb860b37f3.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2a8d3e6e699e824fdbb0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-71cb3f8bebcb860b37f3.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-2a8d3e6e699e824fdbb0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-9c34d6c248976851229f.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}