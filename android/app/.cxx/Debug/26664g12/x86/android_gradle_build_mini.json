{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\TRABALHO\\PROMOBELL - LTDA\\promobell\\android\\app\\.cxx\\Debug\\26664g12\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\TRABALHO\\PROMOBELL - LTDA\\promobell\\android\\app\\.cxx\\Debug\\26664g12\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}