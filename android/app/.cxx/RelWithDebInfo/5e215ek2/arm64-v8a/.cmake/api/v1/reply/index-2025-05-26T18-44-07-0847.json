{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "H:/Android/cmake/3.22.1/bin/cmake.exe", "cpack": "H:/Android/cmake/3.22.1/bin/cpack.exe", "ctest": "H:/Android/cmake/3.22.1/bin/ctest.exe", "root": "H:/Android/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-901b0f24d911b07f1061.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-4765c5417ea3ad436260.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b1d66fda5cf0ff7ae2d9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-4765c5417ea3ad436260.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-b1d66fda5cf0ff7ae2d9.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-901b0f24d911b07f1061.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}