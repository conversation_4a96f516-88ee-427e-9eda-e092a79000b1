# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: RelWithDebInfo
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = RelWithDebInfo
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = H$:/projetos/chegou$ mercado/promobell/android/app/.cxx/RelWithDebInfo/5e215ek2/x86/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "H:\projetos\chegou mercado\promobell\android\app\.cxx\RelWithDebInfo\5e215ek2\x86" && H:\Android\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "H:\projetos\chegou mercado\promobell\android\app\.cxx\RelWithDebInfo\5e215ek2\x86" && H:\Android\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SH:\flutter\packages\flutter_tools\gradle\src\main\groovy -B"H:\projetos\chegou mercado\promobell\android\app\.cxx\RelWithDebInfo\5e215ek2\x86""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: H:/projetos/chegou mercado/promobell/android/app/.cxx/RelWithDebInfo/5e215ek2/x86

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake H$:/Android/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake H$:/Android/ndk/26.3.11579264/build/cmake/android.toolchain.cmake H$:/Android/ndk/26.3.11579264/build/cmake/flags.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake H$:/Android/ndk/26.3.11579264/build/cmake/platforms.cmake H$:/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake H$:/Android/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake H$:/Android/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake H$:/Android/ndk/26.3.11579264/build/cmake/android.toolchain.cmake H$:/Android/ndk/26.3.11579264/build/cmake/flags.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake H$:/Android/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake H$:/Android/ndk/26.3.11579264/build/cmake/platforms.cmake H$:/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
